#!/usr/bin/env python3
"""
测试修复后的代码
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from auditluma.rag.best_practices import BestPracticesIndex, PracticeRule, StandardSource
from auditluma.utils import check_ollama_service, init_llm_client
from loguru import logger


async def test_practice_rule_hashable():
    """测试PracticeRule是否可哈希"""
    print("测试1: PracticeRule可哈希性")
    
    try:
        # 创建测试规则
        rule1 = PracticeRule(
            id="test_001",
            pattern=r"test_pattern",
            language="python",
            vulnerability_type="test",
            severity="LOW",
            title="测试规则",
            description="测试描述",
            recommendation="测试建议",
            source=StandardSource.CUSTOM
        )
        
        rule2 = PracticeRule(
            id="test_002",
            pattern=r"test_pattern2",
            language="python",
            vulnerability_type="test",
            severity="LOW",
            title="测试规则2",
            description="测试描述2",
            recommendation="测试建议2",
            source=StandardSource.CUSTOM
        )
        
        # 测试是否可以添加到set中
        rule_set = {rule1, rule2}
        print(f"✓ 成功创建包含 {len(rule_set)} 个规则的集合")
        
        # 测试相等性
        rule1_copy = PracticeRule(
            id="test_001",  # 相同ID
            pattern=r"different_pattern",
            language="java",
            vulnerability_type="different",
            severity="HIGH",
            title="不同标题",
            description="不同描述",
            recommendation="不同建议",
            source=StandardSource.OWASP
        )
        
        print(f"✓ rule1 == rule1_copy: {rule1 == rule1_copy}")
        print(f"✓ hash(rule1) == hash(rule1_copy): {hash(rule1) == hash(rule1_copy)}")
        
        return True
        
    except Exception as e:
        print(f"✗ PracticeRule可哈希性测试失败: {e}")
        return False


async def test_best_practices_matching():
    """测试最佳实践匹配"""
    print("\n测试2: 最佳实践匹配")
    
    try:
        # 创建索引
        index = BestPracticesIndex()
        
        # 等待规则加载完成
        await asyncio.sleep(2)
        
        # 测试代码匹配
        test_code = """
        SELECT * FROM users WHERE id = " + user_id + "
        """
        
        matches = await index.match_best_practices(test_code, "sql")
        print(f"✓ 找到 {len(matches)} 个匹配的最佳实践")
        
        for match in matches[:3]:  # 只显示前3个
            print(f"  - {match.title}: {match.description[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ 最佳实践匹配测试失败: {e}")
        return False


async def test_ollama_service():
    """测试Ollama服务连接"""
    print("\n测试3: Ollama服务连接")
    
    try:
        # 检查服务状态
        is_available = await check_ollama_service()
        
        if is_available:
            print("✓ Ollama服务运行正常")
            
            # 尝试初始化客户端
            try:
                client = init_llm_client("qwen2.5:7b@ollama")
                print("✓ Ollama客户端初始化成功")
                
                # 尝试简单的API调用
                try:
                    response = await client.chat.completions.create(
                        model="qwen2.5:7b",
                        messages=[{"role": "user", "content": "Hello, this is a test."}],
                        temperature=0.7,
                        max_tokens=50
                    )
                    
                    if response.choices and response.choices[0].message:
                        content = response.choices[0].message.content
                        print(f"✓ Ollama API调用成功，响应: {content[:100]}...")
                        return True
                    else:
                        print("✗ Ollama API响应格式异常")
                        return False
                        
                except Exception as api_error:
                    print(f"✗ Ollama API调用失败: {api_error}")
                    return False
                    
            except Exception as client_error:
                print(f"✗ Ollama客户端初始化失败: {client_error}")
                return False
        else:
            print("✗ Ollama服务不可用")
            print("  请确保:")
            print("  1. Ollama已安装并启动")
            print("  2. 运行 'ollama serve' 启动服务")
            print("  3. 运行 'ollama pull qwen2.5:7b' 下载模型")
            return False
            
    except Exception as e:
        print(f"✗ Ollama服务测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始测试修复后的代码...\n")
    
    results = []
    
    # 测试1: PracticeRule可哈希性
    results.append(await test_practice_rule_hashable())
    
    # 测试2: 最佳实践匹配
    results.append(await test_best_practices_matching())
    
    # 测试3: Ollama服务
    results.append(await test_ollama_service())
    
    # 总结
    print(f"\n测试总结:")
    print(f"通过: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✓ 所有测试通过！")
        return 0
    else:
        print("✗ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)