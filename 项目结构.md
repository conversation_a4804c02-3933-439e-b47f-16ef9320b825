# AuditLuma 项目结构

## 项目概述
AuditLuma 是一个高级代码审计AI系统，基于层级RAG架构实现自动化代码安全审计和漏洞检测。

## 完整目录结构

```
AuditLuma/
├── auditluma/                    # 核心 Python 包
│   ├── agents/                    # AI 代理实现
│   │   ├── __init__.py
│   │   ├── base.py                # 代理基类
│   │   ├── code_analyzer.py       # 代码分析代理
│   │   ├── orchestrator.py        # 代理编排器
│   │   ├── remediation.py         # 修复建议代理
│   │   └── security_analyst.py    # 安全分析代理
│   │
│   ├── analyzers/               # 分析器模块
│   │   ├── __init__.py
│   │   ├── global_context_analyzer.py
│   │   ├── cross_file_analyzer.py
│   │   └── dataflow_analyzer.py
│   │
│   ├── mcp/                     # 模型控制平面
│   │   ├── __init__.py
│   │   ├── protocol.py            # 通信协议
│   │   ├── server.py              # MCP服务器
│   │   └── client.py              # MCP客户端
│   │
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── code.py                # 代码相关模型
│   │   ├── vulnerability.py       # 漏洞模型
│   │   ├── cvss4.py              # CVSS 4.0模型
│   │   └── hierarchical_rag.py    # 层级RAG模型
│   │
│   ├── orchestrator/            # 🚀 编排器模块（层级RAG核心）
│   │   ├── __init__.py
│   │   ├── haystack_orchestrator.py      # 传统Haystack编排器
│   │   ├── haystack_ai_orchestrator.py   # ⭐ Haystack-AI编排器（推荐）
│   │   ├── task_decomposer.py            # 任务分解器
│   │   ├── parallel_executor.py          # 并行执行器
│   │   ├── result_integrator.py          # 结果整合器
│   │   └── error_handling.py             # 错误处理
│   │
│   ├── parsers/                 # 代码解析器
│   │   ├── __init__.py
│   │   ├── code_parser.py         # 通用代码解析器
│   │   ├── python_parser.py       # Python解析器
│   │   ├── javascript_parser.py   # JavaScript解析器
│   │   └── java_parser.py         # Java解析器
│   │
│   ├── rag/                     # 🏗️ RAG相关模块（层级RAG各层实现）
│   │   ├── __init__.py
│   │   ├── self_rag.py            # Self-RAG实现
│   │   ├── txtai_retriever.py     # txtai知识检索层
│   │   ├── r2r_enhancer.py        # R2R上下文增强层
│   │   └── self_rag_validator.py  # Self-RAG验证层
│   │
│   ├── migration/               # 配置迁移
│   │   ├── __init__.py
│   │   └── config_migrator.py     # 配置迁移工具
│   │
│   ├── templates/               # 报告模板
│   │   └── report.html           # HTML 报告模板
│   │
│   ├── visualizer/              # 可视化组件
│   │   ├── __init__.py
│   │   ├── graph_visualizer.py    # 图表可视化
│   │   ├── report_generator.py    # 报告生成器
│   │   └── templates/             # 可视化模板
│   │       └── report_template.html
│   │
│   ├── utils/                   # 工具函数
│   │   ├── __init__.py
│   │   ├── file_utils.py          # 文件操作工具
│   │   ├── security_utils.py      # 安全工具
│   │   └── visualization.py       # 可视化工具
│   │
│   ├── __init__.py
│   ├── config.py                  # 📋 统一配置管理
│   ├── orchestrator.py            # 主编排器
│   ├── scanner.py                 # 代码扫描器
│   └── utils.py                   # 工具函数
│
├── config/                      # 配置文件目录
│   ├── config.yaml.example        # 主配置文件示例（包含Haystack-AI配置）
│   ├── api_config.yaml.example    # API配置示例
│   ├── hierarchical_rag_config.yaml  # 详细的层级RAG配置
│   └── backups/                   # 配置备份目录
│
├── tests/                       # 测试文件目录
│   ├── __init__.py
│   ├── test_agents.py             # 智能体测试
│   ├── test_analyzers.py          # 分析器测试
│   ├── test_models.py             # 模型测试
│   ├── test_orchestrator.py       # 编排器测试
│   ├── test_parsers.py            # 解析器测试
│   ├── test_rag.py                # RAG测试
│   ├── test_hierarchical_rag_config.py  # 层级RAG配置测试
│   ├── test_hierarchical_rag_models.py  # 层级RAG模型测试
│   └── test_error_handling.py     # 错误处理测试
│
├── data/                        # 数据目录
│   ├── vulnerability_db/          # 漏洞数据库
│   ├── best_practices/            # 最佳实践库
│   ├── historical_cases/          # 历史案例
│   └── cache/                     # 缓存目录
│
├── lib/                         # 第三方库
│   ├── bindings/                 # 语言绑定
│   │   └── utils.js
│   ├── tom-select/               # 选择框组件
│   └── vis-9.1.2/                # 可视化库
│
├── docs/                        # 文档目录
│   ├── api.md                     # API文档
│   ├── architecture.md            # 架构文档
│   ├── configuration.md           # 配置文档
│   └── development.md             # 开发文档
│
├── demo_haystack_ai_orchestrator.py  # 🚀 Haystack-AI演示脚本
├── validate_haystack_config.py       # 配置验证脚本
├── Haystack-AI集成指南.md            # 📖 Haystack-AI集成指南
├── 项目结构.md                       # 项目结构说明（本文件）
├── main.py                      # 主程序入口
├── requirements.txt             # Python依赖（包含haystack-ai）
├── glconfig.py                  # 全局配置
├── README.md                    # 项目说明
└── LICENSE                      # 许可证文件
```

## 主要组件说明

### 🚀 层级RAG架构核心
- **`auditluma/orchestrator/`**: 编排器模块，层级RAG架构的核心
  - `haystack_ai_orchestrator.py`: ⭐ 基于Haystack-AI的主编排器（推荐使用）
  - `task_decomposer.py`: 任务分解器，将审计任务分解为子任务
  - `parallel_executor.py`: 并行执行器，高效执行多个任务
  - `result_integrator.py`: 结果整合器，智能合并和去重结果

### 🏗️ 层级RAG各层实现
- **`auditluma/rag/`**: RAG相关模块
  - `txtai_retriever.py`: txtai知识检索层，实时检索CVE和最佳实践
  - `r2r_enhancer.py`: R2R上下文增强层，深度代码上下文分析
  - `self_rag_validator.py`: Self-RAG验证层，智能验证和假阳性过滤
  - `self_rag.py`: 传统Self-RAG实现

### 🤖 AI代理系统
- **`auditluma/agents/`**: AI代理实现
  - `base.py`: 代理基类
  - `code_analyzer.py`: 代码分析代理
  - `security_analyst.py`: 安全分析代理
  - `remediation.py`: 修复建议代理
  - `orchestrator.py`: 代理编排器

### 📊 数据模型和分析器
- **`auditluma/models/`**: 数据模型定义
  - `code.py`: 代码相关模型
  - `vulnerability.py`: 漏洞模型
  - `cvss4.py`: CVSS 4.0评分模型
  - `hierarchical_rag.py`: 层级RAG模型
- **`auditluma/analyzers/`**: 高级分析器
  - `global_context_analyzer.py`: 全局上下文分析
  - `cross_file_analyzer.py`: 跨文件分析
  - `dataflow_analyzer.py`: 数据流分析

### ⚙️ 配置和工具
- **`auditluma/config.py`**: 📋 统一配置管理系统
- **`config/`**: 配置文件目录
  - `config.yaml.example`: 主配置文件示例（包含Haystack-AI配置）
  - `hierarchical_rag_config.yaml`: 详细的层级RAG配置
- **`auditluma/parsers/`**: 代码解析器，支持多种编程语言
- **`auditluma/utils/`**: 工具函数和实用程序

### 📖 文档和演示
- **`Haystack-AI集成指南.md`**: 📖 完整的Haystack-AI集成和使用指南
- **`demo_haystack_ai_orchestrator.py`**: 🚀 Haystack-AI演示脚本
- **`validate_haystack_config.py`**: 配置验证脚本

## 开发指南

### 环境要求
- Python 3.8+
- OpenAI API密钥（用于Haystack-AI）
- 依赖管理: `requirements.txt`（包含haystack-ai）

### 快速开始

1. 安装依赖:
   ```bash
   pip install -r requirements.txt
   ```

2. 配置环境变量:
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   ```

3. 验证Haystack-AI配置:
   ```bash
   python validate_haystack_config.py
   ```

4. 运行Haystack-AI演示:
   ```bash
   python demo_haystack_ai_orchestrator.py
   ```

5. 运行主程序:
   ```bash
   python main.py -d /path/to/target/code -o ./reports
   ```

### 🚀 使用Haystack-AI层级RAG架构

```python
from auditluma.orchestrator.haystack_ai_orchestrator import HaystackAIOrchestrator
from auditluma.models.code import SourceFile

# 初始化编排器（自动从config.yaml读取配置）
orchestrator = HaystackAIOrchestrator()

# 执行审计
result = await orchestrator.orchestrate_audit(source_files)
```

### 配置说明

在 `config.yaml` 中配置层级RAG：

```yaml
hierarchical_rag:
  enabled: true
  haystack:
    enabled: true
    use_haystack_ai: true
    model_name: "gpt-3.5-turbo"
    max_workers: 10
```

详细配置请参考：`Haystack-AI集成指南.md`

## 贡献指南

1. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
2. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
3. 推送到分支 (`git push origin feature/AmazingFeature`)
4. 创建 Pull Request

## 许可证
[MIT]
