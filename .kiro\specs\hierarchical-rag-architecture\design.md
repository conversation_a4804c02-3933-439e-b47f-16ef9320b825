# 层级RAG架构设计文档

## 概述

本设计文档详细描述了AuditLuma系统从单层RAG架构升级到四层层级RAG架构的技术实现方案。新架构通过Haystack编排、txtai知识检索、R2R上下文增强和Self-RAG验证四个层次的协同工作，实现更准确、更全面的代码安全审计。

## 架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "输入层"
        A[源代码文件] --> B[代码扫描器]
        B --> C[SourceFile对象]
    end
    
    subgraph "第一层: Haystack编排框架"
        C --> D[HaystackOrchestrator]
        D --> E[任务分解器]
        E --> F[语法检查任务]
        E --> G[逻辑分析任务]
        E --> H[安全扫描任务]
        E --> I[依赖分析任务]
    end
    
    subgraph "第二层: txtai知识检索"
        F --> J[TxtaiRetriever]
        G --> J
        H --> J
        I --> J
        J --> K[CVE数据库]
        J --> L[最佳实践库]
        J --> M[历史案例库]
    end
    
    subgraph "第三层: R2R上下文增强"
        J --> N[R2REnhancer]
        N --> O[调用图构建器]
        N --> P[数据流分析器]
        N --> Q[影响面评估器]
        N --> R[语义上下文扩展器]
    end
    
    subgraph "第四层: Self-RAG验证"
        N --> S[SelfRAGValidator]
        S --> T[交叉验证器]
        S --> U[置信度计算器]
        S --> V[假阳性过滤器]
    end
    
    subgraph "输出层"
        S --> W[结果整合器]
        W --> X[审计报告]
        W --> Y[漏洞列表]
        W --> Z[置信度评分]
    end
```

### 数据流设计

```mermaid
sequenceDiagram
    participant SC as 代码扫描器
    participant HO as Haystack编排器
    participant TR as txtai检索器
    participant RE as R2R增强器
    participant SV as Self-RAG验证器
    participant RI as 结果整合器
    
    SC->>HO: 源文件列表
    HO->>HO: 任务分解
    
    par 并行处理
        HO->>TR: 语法检查任务
        HO->>TR: 逻辑分析任务
        HO->>TR: 安全扫描任务
        HO->>TR: 依赖分析任务
    end
    
    TR->>RE: 知识增强结果
    RE->>RE: 上下文分析
    RE->>SV: 增强后结果
    SV->>SV: 验证和评分
    SV->>RI: 验证后结果
    RI->>RI: 结果整合
    RI-->>HO: 最终审计报告
```

## 组件设计

### 1. Haystack编排层 (HaystackOrchestrator)

#### 核心职责
- 任务分解和调度
- 并行处理管理
- 结果汇总和整合
- 错误处理和重试

#### 接口设计

```python
class HaystackOrchestrator:
    """Haystack主编排器"""
    
    def __init__(self, workers: int = 10):
        self.workers = workers
        self.txtai_retriever = TxtaiRetriever()
        self.r2r_enhancer = R2REnhancer()
        self.self_rag_validator = SelfRAGValidator()
        self.task_queue = asyncio.Queue()
        self.result_queue = asyncio.Queue()
        self.performance_monitor = PerformanceMonitor()
    
    async def orchestrate_audit(self, source_files: List[SourceFile]) -> AuditResult:
        """主编排流程"""
        
    async def decompose_audit_tasks(self, source_files: List[SourceFile]) -> TaskCollection:
        """任务分解"""
        
    async def execute_parallel_tasks(self, tasks: TaskCollection) -> List[TaskResult]:
        """并行执行任务"""
        
    async def integrate_results(self, task_results: List[TaskResult]) -> AuditResult:
        """结果整合"""
```

#### 任务分解策略

```python
@dataclass
class AuditTask:
    """审计任务定义"""
    id: str
    type: TaskType  # SYNTAX_CHECK, LOGIC_ANALYSIS, SECURITY_SCAN, DEPENDENCY_ANALYSIS
    priority: int
    source_files: List[SourceFile]
    dependencies: List[str]  # 依赖的其他任务ID
    timeout: int
    metadata: Dict[str, Any]

class TaskDecomposer:
    """任务分解器"""
    
    def decompose(self, source_files: List[SourceFile]) -> TaskCollection:
        """将代码审计分解为具体任务"""
        tasks = []
        
        # 1. 语法检查任务（优先级最高，无依赖）
        syntax_tasks = self._create_syntax_check_tasks(source_files)
        tasks.extend(syntax_tasks)
        
        # 2. 逻辑分析任务（依赖语法检查）
        logic_tasks = self._create_logic_analysis_tasks(source_files, syntax_tasks)
        tasks.extend(logic_tasks)
        
        # 3. 安全扫描任务（依赖逻辑分析）
        security_tasks = self._create_security_scan_tasks(source_files, logic_tasks)
        tasks.extend(security_tasks)
        
        # 4. 依赖分析任务（可并行执行）
        dependency_tasks = self._create_dependency_analysis_tasks(source_files)
        tasks.extend(dependency_tasks)
        
        return TaskCollection(tasks)
```

### 2. txtai知识检索层 (TxtaiRetriever)

#### 核心职责
- CVE数据库实时查询
- 最佳实践匹配
- 历史案例检索
- 知识源管理和更新

#### 接口设计

```python
class TxtaiRetriever:
    """txtai实时知识检索器"""
    
    def __init__(self):
        self.cve_client = CVEDatabaseClient()
        self.best_practices_index = BestPracticesIndex()
        self.historical_cases_index = HistoricalCasesIndex()
        self.knowledge_cache = KnowledgeCache()
    
    async def retrieve_vulnerability_info(self, code_pattern: str, 
                                        context: Dict[str, Any]) -> VulnerabilityKnowledge:
        """检索漏洞相关信息"""
        
    async def query_cve_database(self, vulnerability_signature: str) -> List[CVEInfo]:
        """实时查询CVE数据库"""
        
    async def match_best_practices(self, code_pattern: str, 
                                 language: str) -> List[BestPractice]:
        """匹配最佳实践"""
        
    async def search_historical_cases(self, code_pattern: str, 
                                    similarity_threshold: float = 0.8) -> List[HistoricalCase]:
        """搜索历史案例"""
```

#### 知识源管理

```python
class KnowledgeSourceManager:
    """知识源管理器"""
    
    def __init__(self):
        self.sources = {
            'cve': CVESource(),
            'owasp': OWASPSource(),
            'sans': SANSSource(),
            'nist': NISTSource(),
            'custom': CustomRulesSource()
        }
        self.update_scheduler = UpdateScheduler()
    
    async def update_knowledge_sources(self):
        """更新知识源"""
        for name, source in self.sources.items():
            try:
                await source.update()
                logger.info(f"知识源 {name} 更新成功")
            except Exception as e:
                logger.error(f"知识源 {name} 更新失败: {e}")
    
    async def query_all_sources(self, query: str) -> List[KnowledgeItem]:
        """查询所有知识源"""
        results = []
        tasks = [source.query(query) for source in self.sources.values()]
        source_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in source_results:
            if not isinstance(result, Exception):
                results.extend(result)
        
        return results
```

### 3. R2R上下文增强层 (R2REnhancer)

#### 核心职责
- 调用链构建和分析
- 数据流追踪
- 影响面评估
- 语义上下文扩展

#### 接口设计

```python
class R2REnhancer:
    """R2R代码上下文增强器"""
    
    def __init__(self):
        self.call_graph_builder = CallGraphBuilder()
        self.dataflow_analyzer = DataFlowAnalyzer()
        self.impact_assessor = ImpactAssessor()
        self.context_expander = ContextExpander()
        self.semantic_analyzer = SemanticAnalyzer()
    
    async def enhance_context(self, vulnerability: VulnerabilityResult, 
                            global_context: GlobalContext) -> EnhancedContext:
        """增强代码上下文"""
        
    async def build_call_graph(self, source_files: List[SourceFile]) -> CallGraph:
        """构建调用图"""
        
    async def analyze_data_flow(self, vulnerability: VulnerabilityResult, 
                              call_graph: CallGraph) -> DataFlowInfo:
        """分析数据流"""
        
    async def assess_impact_scope(self, vulnerability: VulnerabilityResult, 
                                call_graph: CallGraph, 
                                data_flow: DataFlowInfo) -> ImpactScope:
        """评估影响范围"""
```

#### 调用图构建

```python
class CallGraphBuilder:
    """调用图构建器"""
    
    def __init__(self):
        self.parsers = {
            'python': PythonCallGraphParser(),
            'javascript': JavaScriptCallGraphParser(),
            'java': JavaCallGraphParser(),
            'csharp': CSharpCallGraphParser()
        }
    
    async def build_graph(self, source_files: List[SourceFile]) -> CallGraph:
        """构建完整的调用图"""
        graph = CallGraph()
        
        # 1. 解析每个文件的函数定义和调用
        for file in source_files:
            parser = self.parsers.get(file.language)
            if parser:
                file_graph = await parser.parse_file(file)
                graph.merge(file_graph)
        
        # 2. 解析跨文件调用关系
        await self._resolve_cross_file_calls(graph, source_files)
        
        # 3. 构建调用链
        await self._build_call_chains(graph)
        
        return graph
    
    async def _resolve_cross_file_calls(self, graph: CallGraph, 
                                      source_files: List[SourceFile]):
        """解析跨文件调用关系"""
        # 实现跨文件调用解析逻辑
        pass
```

#### 数据流分析

```python
class DataFlowAnalyzer:
    """数据流分析器"""
    
    def __init__(self):
        self.taint_analyzer = TaintAnalyzer()
        self.variable_tracker = VariableTracker()
    
    async def analyze_flow(self, vulnerability: VulnerabilityResult, 
                         call_graph: CallGraph) -> DataFlowInfo:
        """分析数据流"""
        
        # 1. 污点分析
        taint_info = await self.taint_analyzer.analyze(vulnerability, call_graph)
        
        # 2. 变量追踪
        variable_info = await self.variable_tracker.track(vulnerability, call_graph)
        
        # 3. 构建数据流图
        flow_graph = self._build_flow_graph(taint_info, variable_info)
        
        return DataFlowInfo(
            taint_info=taint_info,
            variable_info=variable_info,
            flow_graph=flow_graph
        )
```

### 4. Self-RAG验证层 (SelfRAGValidator)

#### 核心职责
- 交叉验证
- 置信度计算
- 假阳性过滤
- 结果质量保证

#### 接口设计

```python
class SelfRAGValidator:
    """Self-RAG自我验证器"""
    
    def __init__(self):
        self.cross_validator = CrossValidator()
        self.confidence_calculator = ConfidenceCalculator()
        self.false_positive_filter = FalsePositiveFilter()
        self.quality_assessor = QualityAssessor()
    
    async def validate_results(self, results: List[VulnerabilityResult], 
                             enhanced_contexts: List[EnhancedContext]) -> ValidatedResults:
        """验证审计结果"""
        
    async def cross_validate(self, result: VulnerabilityResult, 
                           context: EnhancedContext) -> CrossValidationResult:
        """交叉验证"""
        
    async def calculate_confidence(self, result: VulnerabilityResult, 
                                 context: EnhancedContext) -> ConfidenceScore:
        """计算置信度"""
        
    async def filter_false_positives(self, results: List[VulnerabilityResult]) -> List[VulnerabilityResult]:
        """过滤假阳性"""
```

#### 置信度计算模型

```python
class ConfidenceCalculator:
    """置信度计算器"""
    
    def __init__(self):
        self.weights = {
            'code_quality': 0.2,
            'pattern_match': 0.25,
            'context_completeness': 0.2,
            'historical_accuracy': 0.15,
            'cross_validation': 0.2
        }
    
    async def calculate(self, result: VulnerabilityResult, 
                      context: EnhancedContext) -> ConfidenceScore:
        """计算综合置信度"""
        
        scores = {}
        
        # 1. 代码质量评分
        scores['code_quality'] = await self._assess_code_quality(result, context)
        
        # 2. 模式匹配评分
        scores['pattern_match'] = await self._assess_pattern_match(result, context)
        
        # 3. 上下文完整性评分
        scores['context_completeness'] = await self._assess_context_completeness(context)
        
        # 4. 历史准确性评分
        scores['historical_accuracy'] = await self._assess_historical_accuracy(result)
        
        # 5. 交叉验证评分
        scores['cross_validation'] = await self._assess_cross_validation(result, context)
        
        # 计算加权平均
        weighted_score = sum(scores[key] * self.weights[key] for key in scores)
        
        return ConfidenceScore(
            overall_score=weighted_score,
            component_scores=scores,
            explanation=self._generate_explanation(scores)
        )
```

## 数据模型设计

### 核心数据结构

```python
@dataclass
class AuditResult:
    """审计结果"""
    id: str
    vulnerabilities: List[VulnerabilityResult]
    processing_time: float
    confidence_score: float
    task_results: List[TaskResult]
    execution_summary: Dict[str, Any]
    metadata: Dict[str, Any]

@dataclass
class EnhancedContext:
    """增强上下文"""
    call_chain: CallChain
    data_flow: DataFlowInfo
    impact_scope: ImpactScope
    semantic_context: SemanticContext
    completeness_score: float

@dataclass
class VulnerabilityKnowledge:
    """漏洞知识"""
    cve_info: List[CVEInfo]
    best_practices: List[BestPractice]
    historical_cases: List[HistoricalCase]
    relevance_scores: Dict[str, float]

@dataclass
class ValidatedResults:
    """验证后的结果"""
    validated_vulnerabilities: List[VulnerabilityResult]
    filtered_count: int
    confidence_distribution: Dict[str, int]
    validation_summary: ValidationSummary
```

## 错误处理和容错设计

### 分层错误处理

```python
class HierarchicalErrorHandler:
    """层级错误处理器"""
    
    def __init__(self):
        self.layer_handlers = {
            'haystack': HaystackErrorHandler(),
            'txtai': TxtaiErrorHandler(),
            'r2r': R2RErrorHandler(),
            'self_rag': SelfRAGErrorHandler()
        }
        self.fallback_strategies = FallbackStrategies()
    
    async def handle_error(self, error: Exception, layer: str, context: Dict[str, Any]):
        """处理特定层的错误"""
        handler = self.layer_handlers.get(layer)
        if handler:
            return await handler.handle(error, context)
        
        # 使用通用错误处理
        return await self._handle_generic_error(error, context)
    
    async def apply_fallback(self, layer: str, context: Dict[str, Any]):
        """应用回退策略"""
        strategy = self.fallback_strategies.get_strategy(layer)
        return await strategy.execute(context)
```

### 容错机制

```python
class FaultToleranceManager:
    """容错管理器"""
    
    def __init__(self):
        self.circuit_breakers = {}
        self.retry_policies = {}
        self.health_checkers = {}
    
    async def execute_with_tolerance(self, func, layer: str, *args, **kwargs):
        """带容错的执行"""
        circuit_breaker = self.circuit_breakers.get(layer)
        retry_policy = self.retry_policies.get(layer)
        
        if circuit_breaker and circuit_breaker.is_open():
            raise CircuitBreakerOpenError(f"Circuit breaker open for layer {layer}")
        
        for attempt in range(retry_policy.max_attempts):
            try:
                result = await func(*args, **kwargs)
                if circuit_breaker:
                    circuit_breaker.record_success()
                return result
            except Exception as e:
                if circuit_breaker:
                    circuit_breaker.record_failure()
                
                if attempt == retry_policy.max_attempts - 1:
                    raise
                
                await asyncio.sleep(retry_policy.delay * (2 ** attempt))
```

## 性能优化设计

### 缓存策略

```python
class HierarchicalCache:
    """层级缓存系统"""
    
    def __init__(self):
        self.l1_cache = MemoryCache(size="256MB")  # 内存缓存
        self.l2_cache = DiskCache(size="2GB")      # 磁盘缓存
        self.distributed_cache = RedisCache()      # 分布式缓存（可选）
    
    async def get(self, key: str) -> Optional[Any]:
        """多级缓存获取"""
        # L1缓存
        value = await self.l1_cache.get(key)
        if value is not None:
            return value
        
        # L2缓存
        value = await self.l2_cache.get(key)
        if value is not None:
            await self.l1_cache.set(key, value)
            return value
        
        # 分布式缓存
        if self.distributed_cache:
            value = await self.distributed_cache.get(key)
            if value is not None:
                await self.l1_cache.set(key, value)
                await self.l2_cache.set(key, value)
                return value
        
        return None
    
    async def set(self, key: str, value: Any, ttl: int = 3600):
        """多级缓存设置"""
        await self.l1_cache.set(key, value, ttl)
        await self.l2_cache.set(key, value, ttl)
        if self.distributed_cache:
            await self.distributed_cache.set(key, value, ttl)
```

### 并行处理优化

```python
class ParallelProcessingManager:
    """并行处理管理器"""
    
    def __init__(self, max_workers: int = 20):
        self.max_workers = max_workers
        self.semaphore = asyncio.Semaphore(max_workers)
        self.task_scheduler = TaskScheduler()
    
    async def process_batch(self, tasks: List[Task]) -> List[TaskResult]:
        """批量并行处理"""
        # 按优先级和依赖关系排序
        sorted_tasks = self.task_scheduler.schedule(tasks)
        
        # 分批处理
        results = []
        for batch in self._create_batches(sorted_tasks):
            batch_results = await self._process_batch_parallel(batch)
            results.extend(batch_results)
        
        return results
    
    async def _process_batch_parallel(self, batch: List[Task]) -> List[TaskResult]:
        """并行处理单个批次"""
        async def process_task(task):
            async with self.semaphore:
                return await task.execute()
        
        tasks = [process_task(task) for task in batch]
        return await asyncio.gather(*tasks, return_exceptions=True)
```

## 监控和可观测性设计

### 性能监控

```python
class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.performance_tracker = PerformanceTracker()
        self.alerting_system = AlertingSystem()
    
    async def track_layer_performance(self, layer: str, operation: str, 
                                    duration: float, metadata: Dict[str, Any]):
        """跟踪层级性能"""
        metric = PerformanceMetric(
            layer=layer,
            operation=operation,
            duration=duration,
            timestamp=time.time(),
            metadata=metadata
        )
        
        await self.metrics_collector.collect(metric)
        
        # 检查性能阈值
        if duration > self._get_threshold(layer, operation):
            await self.alerting_system.send_alert(
                f"Performance threshold exceeded for {layer}.{operation}: {duration}s"
            )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return self.performance_tracker.get_summary()
```

### 质量监控

```python
class QualityMonitor:
    """质量监控器"""
    
    def __init__(self):
        self.accuracy_tracker = AccuracyTracker()
        self.false_positive_tracker = FalsePositiveTracker()
        self.confidence_tracker = ConfidenceTracker()
    
    async def track_result_quality(self, result: VulnerabilityResult, 
                                 validation_info: ValidationInfo):
        """跟踪结果质量"""
        await self.accuracy_tracker.track(result, validation_info)
        await self.false_positive_tracker.track(result, validation_info)
        await self.confidence_tracker.track(result, validation_info)
    
    def get_quality_metrics(self) -> QualityMetrics:
        """获取质量指标"""
        return QualityMetrics(
            accuracy=self.accuracy_tracker.get_accuracy(),
            false_positive_rate=self.false_positive_tracker.get_rate(),
            average_confidence=self.confidence_tracker.get_average()
        )
```

## 测试策略

### 单元测试

```python
class TestHaystackOrchestrator:
    """Haystack编排器测试"""
    
    @pytest.mark.asyncio
    async def test_task_decomposition(self):
        """测试任务分解"""
        orchestrator = HaystackOrchestrator()
        source_files = create_test_source_files()
        
        tasks = await orchestrator.decompose_audit_tasks(source_files)
        
        assert len(tasks.syntax_tasks) > 0
        assert len(tasks.logic_tasks) > 0
        assert len(tasks.security_tasks) > 0
        assert len(tasks.dependency_tasks) > 0
    
    @pytest.mark.asyncio
    async def test_parallel_execution(self):
        """测试并行执行"""
        orchestrator = HaystackOrchestrator(workers=5)
        tasks = create_test_tasks()
        
        start_time = time.time()
        results = await orchestrator.execute_parallel_tasks(tasks)
        execution_time = time.time() - start_time
        
        assert len(results) == len(tasks)
        assert execution_time < sequential_execution_time
```

### 集成测试

```python
class TestHierarchicalRAGIntegration:
    """层级RAG集成测试"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_audit(self):
        """端到端审计测试"""
        orchestrator = HaystackOrchestrator()
        source_files = load_test_project()
        
        result = await orchestrator.orchestrate_audit(source_files)
        
        assert result.vulnerabilities is not None
        assert result.confidence_score > 0
        assert result.processing_time > 0
        assert len(result.task_results) > 0
    
    @pytest.mark.asyncio
    async def test_layer_interaction(self):
        """测试层间交互"""
        # 测试txtai -> R2R -> Self-RAG的数据流
        pass
```

### 性能测试

```python
class TestPerformance:
    """性能测试"""
    
    @pytest.mark.asyncio
    async def test_scalability(self):
        """可扩展性测试"""
        for file_count in [10, 50, 100, 500]:
            source_files = generate_test_files(file_count)
            
            start_time = time.time()
            result = await orchestrator.orchestrate_audit(source_files)
            execution_time = time.time() - start_time
            
            # 验证性能指标
            assert execution_time < expected_time_for_file_count(file_count)
            assert result.confidence_score > minimum_confidence_threshold
```

## 部署和运维设计

### 配置管理

```python
class HierarchicalRAGConfig:
    """层级RAG配置管理"""
    
    def __init__(self):
        self.config_loader = ConfigLoader()
        self.config_validator = ConfigValidator()
        self.config_watcher = ConfigWatcher()
    
    def load_config(self, config_path: str) -> HierarchicalRAGSettings:
        """加载配置"""
        raw_config = self.config_loader.load(config_path)
        validated_config = self.config_validator.validate(raw_config)
        return HierarchicalRAGSettings.from_dict(validated_config)
    
    def watch_config_changes(self, callback):
        """监听配置变更"""
        self.config_watcher.watch(callback)
```

### 健康检查

```python
class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.layer_checkers = {
            'haystack': HaystackHealthChecker(),
            'txtai': TxtaiHealthChecker(),
            'r2r': R2RHealthChecker(),
            'self_rag': SelfRAGHealthChecker()
        }
    
    async def check_health(self) -> HealthStatus:
        """检查系统健康状态"""
        layer_statuses = {}
        
        for layer, checker in self.layer_checkers.items():
            try:
                status = await checker.check()
                layer_statuses[layer] = status
            except Exception as e:
                layer_statuses[layer] = HealthStatus.UNHEALTHY
                logger.error(f"Health check failed for {layer}: {e}")
        
        overall_status = self._determine_overall_status(layer_statuses)
        
        return HealthStatus(
            overall=overall_status,
            layers=layer_statuses,
            timestamp=time.time()
        )
```

## 安全考虑

### 数据安全

```python
class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.encryption_service = EncryptionService()
        self.access_controller = AccessController()
        self.audit_logger = AuditLogger()
    
    async def secure_data_processing(self, data: Any, context: SecurityContext) -> Any:
        """安全的数据处理"""
        # 访问控制检查
        if not await self.access_controller.check_access(context):
            raise AccessDeniedError("Access denied")
        
        # 数据加密（如果需要）
        if context.requires_encryption:
            data = await self.encryption_service.encrypt(data)
        
        # 审计日志
        await self.audit_logger.log_access(context)
        
        return data
```

### API安全

```python
class APISecurityMiddleware:
    """API安全中间件"""
    
    def __init__(self):
        self.rate_limiter = RateLimiter()
        self.auth_service = AuthenticationService()
        self.input_validator = InputValidator()
    
    async def process_request(self, request: Request) -> Request:
        """处理请求安全"""
        # 速率限制
        await self.rate_limiter.check_rate_limit(request.client_ip)
        
        # 身份验证
        user = await self.auth_service.authenticate(request)
        request.user = user
        
        # 输入验证
        validated_data = await self.input_validator.validate(request.data)
        request.data = validated_data
        
        return request
```

这个设计文档提供了层级RAG架构的完整技术实现方案，涵盖了架构设计、组件接口、数据模型、错误处理、性能优化、监控、测试和部署等各个方面。通过这个设计，可以实现一个高效、可靠、可扩展的层级RAG代码审计系统。