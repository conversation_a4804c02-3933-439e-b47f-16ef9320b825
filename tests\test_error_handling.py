"""
错误处理和容错机制单元测试
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta

from auditluma.orchestrator.error_handling import (
    ErrorInfo, ErrorSeverity, ErrorCategory,
    RetryPolicy, CircuitBreaker, CircuitBreakerConfig, CircuitBreakerState,
    CircuitBreakerOpenError, LayerError<PERSON><PERSON>ler, HaystackErrorHandler,
    TxtaiErrorHandler, R2RError<PERSON>andler, SelfRAGErrorHandler,
    HierarchicalErrorHandler, FaultToleranceManager,
    CacheFallbackStrategy, DefaultValueFallbackStrategy,
    create_default_fault_tolerance_manager, with_fault_tolerance
)


class TestErrorInfo:
    """测试错误信息"""
    
    def test_error_info_creation(self):
        """测试错误信息创建"""
        error = ValueError("Test error")
        error_info = ErrorInfo(
            error_id="test-001",
            layer="haystack",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.MEDIUM,
            message="Test error message",
            exception=error,
            context={"key": "value"}
        )
        
        assert error_info.error_id == "test-001"
        assert error_info.layer == "haystack"
        assert error_info.category == ErrorCategory.VALIDATION
        assert error_info.severity == ErrorSeverity.MEDIUM
        assert error_info.message == "Test error message"
        assert error_info.exception == error
        assert error_info.context == {"key": "value"}
        assert error_info.stack_trace is not None
    
    def test_error_info_to_dict(self):
        """测试错误信息序列化"""
        error_info = ErrorInfo(
            error_id="test-001",
            layer="haystack",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.MEDIUM,
            message="Test error message"
        )
        
        error_dict = error_info.to_dict()
        
        assert error_dict['error_id'] == "test-001"
        assert error_dict['layer'] == "haystack"
        assert error_dict['category'] == "validation"
        assert error_dict['severity'] == "medium"
        assert error_dict['message'] == "Test error message"
        assert 'timestamp' in error_dict


class TestRetryPolicy:
    """测试重试策略"""
    
    def test_retry_policy_creation(self):
        """测试重试策略创建"""
        policy = RetryPolicy(
            max_attempts=5,
            base_delay=2.0,
            exponential_backoff=True,
            retryable_exceptions=[ValueError, TypeError]
        )
        
        assert policy.max_attempts == 5
        assert policy.base_delay == 2.0
        assert policy.exponential_backoff == True
        assert ValueError in policy.retryable_exceptions
        assert TypeError in policy.retryable_exceptions
    
    def test_should_retry(self):
        """测试重试判断"""
        policy = RetryPolicy(
            max_attempts=3,
            retryable_exceptions=[ValueError]
        )
        
        # 测试可重试异常
        assert policy.should_retry(ValueError("test"), 1) == True
        assert policy.should_retry(ValueError("test"), 3) == False  # 超过最大尝试次数
        
        # 测试不可重试异常
        assert policy.should_retry(TypeError("test"), 1) == False
        
        # 测试无限制异常类型
        policy_no_limit = RetryPolicy(max_attempts=3)
        assert policy_no_limit.should_retry(RuntimeError("test"), 1) == True
    
    def test_get_delay(self):
        """测试延迟计算"""
        # 指数退避
        policy = RetryPolicy(base_delay=1.0, exponential_backoff=True, jitter=False)
        assert policy.get_delay(0) == 1.0
        assert policy.get_delay(1) == 2.0
        assert policy.get_delay(2) == 4.0
        
        # 固定延迟
        policy_fixed = RetryPolicy(base_delay=2.0, exponential_backoff=False, jitter=False)
        assert policy_fixed.get_delay(0) == 2.0
        assert policy_fixed.get_delay(1) == 2.0
        assert policy_fixed.get_delay(2) == 2.0
        
        # 最大延迟限制
        policy_max = RetryPolicy(base_delay=1.0, max_delay=5.0, exponential_backoff=True, jitter=False)
        assert policy_max.get_delay(10) == 5.0  # 不超过最大延迟


class TestCircuitBreaker:
    """测试断路器"""
    
    def test_circuit_breaker_creation(self):
        """测试断路器创建"""
        config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=30.0)
        cb = CircuitBreaker("test", config)
        
        assert cb.name == "test"
        assert cb.config.failure_threshold == 3
        assert cb.config.recovery_timeout == 30.0
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.failure_count == 0
    
    def test_circuit_breaker_failure_handling(self):
        """测试断路器失败处理"""
        config = CircuitBreakerConfig(failure_threshold=2)
        cb = CircuitBreaker("test", config)
        
        # 初始状态
        assert cb.is_open() == False
        assert cb.state == CircuitBreakerState.CLOSED
        
        # 记录失败
        cb.record_failure()
        assert cb.failure_count == 1
        assert cb.state == CircuitBreakerState.CLOSED
        
        # 达到失败阈值
        cb.record_failure()
        assert cb.failure_count == 2
        assert cb.state == CircuitBreakerState.OPEN
        assert cb.is_open() == True
    
    def test_circuit_breaker_recovery(self):
        """测试断路器恢复"""
        config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=0.1, success_threshold=2)
        cb = CircuitBreaker("test", config)
        
        # 触发断路器打开
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        
        # 等待恢复时间
        time.sleep(0.2)
        
        # 检查是否转为半开状态
        assert cb.is_open() == False  # 这会触发状态转换
        assert cb.state == CircuitBreakerState.HALF_OPEN
        
        # 记录成功，但未达到成功阈值
        cb.record_success()
        assert cb.state == CircuitBreakerState.HALF_OPEN
        
        # 达到成功阈值
        cb.record_success()
        assert cb.state == CircuitBreakerState.CLOSED
    
    def test_circuit_breaker_state_info(self):
        """测试断路器状态信息"""
        config = CircuitBreakerConfig()
        cb = CircuitBreaker("test", config)
        
        state_info = cb.get_state_info()
        
        assert state_info['name'] == "test"
        assert state_info['state'] == "closed"
        assert state_info['failure_count'] == 0
        assert state_info['success_count'] == 0


class TestLayerErrorHandler:
    """测试层级错误处理器"""
    
    @pytest.mark.asyncio
    async def test_layer_error_handler_basic(self):
        """测试基本错误处理"""
        handler = LayerErrorHandler("test_layer")
        error = ValueError("Test error")
        context = {"key": "value"}
        
        result = await handler.handle(error, context)
        
        assert result['handled'] == True
        assert result['action'] == 'logged'
        assert 'error_id' in result
        assert len(handler.error_history) == 1
        assert handler.error_history[0].layer == "test_layer"
    
    def test_error_categorization(self):
        """测试错误分类"""
        handler = LayerErrorHandler("test_layer")
        
        # 超时错误
        timeout_error = TimeoutError("Operation timed out")
        category = handler._categorize_error(timeout_error)
        assert category == ErrorCategory.TIMEOUT
        
        # 网络错误
        network_error = ConnectionError("Network connection failed")
        category = handler._categorize_error(network_error)
        assert category == ErrorCategory.NETWORK
        
        # 验证错误
        validation_error = ValueError("Invalid input")
        category = handler._categorize_error(validation_error)
        assert category == ErrorCategory.VALIDATION
    
    def test_severity_assessment(self):
        """测试严重程度评估"""
        handler = LayerErrorHandler("test_layer")
        
        # 网络错误应该是高严重程度
        network_error = ConnectionError("Network error")
        severity = handler._assess_severity(network_error, ErrorCategory.NETWORK)
        assert severity == ErrorSeverity.HIGH
        
        # 验证错误应该是中等严重程度
        validation_error = ValueError("Validation error")
        severity = handler._assess_severity(validation_error, ErrorCategory.VALIDATION)
        assert severity == ErrorSeverity.MEDIUM
    
    def test_error_statistics(self):
        """测试错误统计"""
        handler = LayerErrorHandler("test_layer")
        
        # 初始状态
        stats = handler.get_error_statistics()
        assert stats['total_errors'] == 0
        
        # 添加一些错误
        error1 = ValueError("Error 1")
        error2 = TimeoutError("Error 2")
        
        asyncio.run(handler.handle(error1, {}))
        asyncio.run(handler.handle(error2, {}))
        
        stats = handler.get_error_statistics()
        assert stats['total_errors'] == 2
        assert 'category_distribution' in stats
        assert 'severity_distribution' in stats
        assert len(stats['recent_errors']) == 2


class TestSpecificErrorHandlers:
    """测试特定层错误处理器"""
    
    @pytest.mark.asyncio
    async def test_haystack_error_handler(self):
        """测试Haystack错误处理器"""
        handler = HaystackErrorHandler()
        
        # 超时错误
        timeout_error = TimeoutError("Task timeout")
        result = await handler.handle(timeout_error, {})
        
        assert result['action'] == 'task_timeout_handled'
        assert result['recommendation'] == 'increase_task_timeout'
    
    @pytest.mark.asyncio
    async def test_txtai_error_handler(self):
        """测试txtai错误处理器"""
        handler = TxtaiErrorHandler()
        
        # 网络错误
        network_error = ConnectionError("Network error")
        result = await handler.handle(network_error, {})
        
        assert result['action'] == 'fallback_to_cache'
        assert result['recommendation'] == 'check_network_connectivity'


class TestFallbackStrategies:
    """测试回退策略"""
    
    @pytest.mark.asyncio
    async def test_cache_fallback_strategy(self):
        """测试缓存回退策略"""
        strategy = CacheFallbackStrategy()
        context = {'cached_data': {'key': 'value'}}
        
        result = await strategy.execute(context)
        
        assert result['strategy'] == 'cache_fallback'
        assert result['action'] == 'use_cached_data'
        assert result['data'] == {'key': 'value'}
    
    @pytest.mark.asyncio
    async def test_default_value_fallback_strategy(self):
        """测试默认值回退策略"""
        default_values = {'result': 'default', 'score': 0.5}
        strategy = DefaultValueFallbackStrategy(default_values)
        
        result = await strategy.execute({})
        
        assert result['strategy'] == 'default_value_fallback'
        assert result['action'] == 'use_default_values'
        assert result['data'] == default_values


class TestHierarchicalErrorHandler:
    """测试层级错误处理器"""
    
    @pytest.mark.asyncio
    async def test_hierarchical_error_handler(self):
        """测试层级错误处理器"""
        handler = HierarchicalErrorHandler()
        
        # 测试已知层的错误处理
        error = ValueError("Test error")
        result = await handler.handle_error(error, "haystack", {})
        
        assert 'handled' in result
        assert 'error_id' in result
        assert len(handler.global_error_history) > 0
    
    @pytest.mark.asyncio
    async def test_unknown_layer_error_handling(self):
        """测试未知层错误处理"""
        handler = HierarchicalErrorHandler()
        
        error = RuntimeError("Unknown layer error")
        result = await handler.handle_error(error, "unknown_layer", {})
        
        assert result['handled'] == False
        assert 'error_id' in result
        assert result['message'] == "Unknown layer error"
    
    @pytest.mark.asyncio
    async def test_fallback_application(self):
        """测试回退策略应用"""
        handler = HierarchicalErrorHandler()
        
        result = await handler.apply_fallback("haystack", {})
        
        assert 'strategy' in result
        assert 'action' in result
    
    def test_global_error_statistics(self):
        """测试全局错误统计"""
        handler = HierarchicalErrorHandler()
        
        stats = handler.get_global_error_statistics()
        
        assert 'global_error_count' in stats
        assert 'layer_statistics' in stats
        assert 'recent_global_errors' in stats


class TestFaultToleranceManager:
    """测试容错管理器"""
    
    def test_fault_tolerance_manager_creation(self):
        """测试容错管理器创建"""
        manager = FaultToleranceManager()
        
        assert len(manager.circuit_breakers) == 4  # 四个默认层
        assert len(manager.retry_policies) == 4
        assert 'haystack' in manager.circuit_breakers
        assert 'txtai' in manager.circuit_breakers
        assert 'r2r' in manager.circuit_breakers
        assert 'self_rag' in manager.circuit_breakers
    
    @pytest.mark.asyncio
    async def test_execute_with_tolerance_success(self):
        """测试容错执行成功情况"""
        manager = FaultToleranceManager()
        
        async def successful_function(x, y):
            return x + y
        
        result = await manager.execute_with_tolerance(successful_function, "haystack", 1, 2)
        assert result == 3
    
    @pytest.mark.asyncio
    async def test_execute_with_tolerance_retry(self):
        """测试容错执行重试情况"""
        manager = FaultToleranceManager()
        
        # 修改重试策略以加快测试
        manager.retry_policies["haystack"] = RetryPolicy(
            max_attempts=3,
            base_delay=0.01,
            exponential_backoff=False
        )
        
        call_count = 0
        
        async def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ValueError("Temporary failure")
            return "success"
        
        result = await manager.execute_with_tolerance(failing_function, "haystack")
        assert result == "success"
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_execute_with_tolerance_circuit_breaker(self):
        """测试断路器功能"""
        manager = FaultToleranceManager()
        
        # 设置低阈值的断路器
        config = CircuitBreakerConfig(failure_threshold=1)
        manager.add_circuit_breaker("test_layer", config)
        
        async def always_failing_function():
            raise RuntimeError("Always fails")
        
        # 第一次调用应该失败并触发断路器
        result1 = await manager.execute_with_tolerance(always_failing_function, "test_layer")
        
        # 第二次调用应该直接使用回退策略
        result2 = await manager.execute_with_tolerance(always_failing_function, "test_layer")
        
        # 验证断路器状态
        cb = manager.circuit_breakers["test_layer"]
        assert cb.state == CircuitBreakerState.OPEN
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """测试健康检查"""
        manager = FaultToleranceManager()
        
        # 添加健康检查器
        async def healthy_checker():
            return {"status": "ok"}
        
        def unhealthy_checker():
            raise RuntimeError("Service down")
        
        manager.add_health_checker("service1", healthy_checker)
        manager.add_health_checker("service2", unhealthy_checker)
        
        health_status = await manager.check_health()
        
        assert health_status['health_checks']['service1']['healthy'] == True
        assert health_status['health_checks']['service2']['healthy'] == False
        assert 'circuit_breakers' in health_status
        assert 'error_statistics' in health_status
    
    def test_fault_tolerance_metrics(self):
        """测试容错指标"""
        manager = FaultToleranceManager()
        
        metrics = manager.get_fault_tolerance_metrics()
        
        assert 'circuit_breakers' in metrics
        assert 'retry_policies' in metrics
        assert 'error_statistics' in metrics
        
        # 检查断路器指标
        assert 'haystack' in metrics['circuit_breakers']
        assert 'txtai' in metrics['circuit_breakers']
        
        # 检查重试策略指标
        assert 'haystack' in metrics['retry_policies']
        assert metrics['retry_policies']['haystack']['max_attempts'] == 3


class TestUtilityFunctions:
    """测试工具函数"""
    
    def test_create_default_fault_tolerance_manager(self):
        """测试创建默认容错管理器"""
        manager = create_default_fault_tolerance_manager()
        
        assert isinstance(manager, FaultToleranceManager)
        assert len(manager.circuit_breakers) == 4
        assert len(manager.retry_policies) == 4
    
    @pytest.mark.asyncio
    async def test_fault_tolerance_decorator(self):
        """测试容错装饰器"""
        
        @with_fault_tolerance("haystack")
        async def decorated_function(x, y):
            return x * y
        
        result = await decorated_function(3, 4)
        assert result == 12


if __name__ == "__main__":
    pytest.main([__file__])