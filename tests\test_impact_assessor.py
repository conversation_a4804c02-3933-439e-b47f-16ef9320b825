"""
影响面评估器单元测试
"""

import pytest
import asyncio
import networkx as nx
from unittest.mock import Mock, patch, AsyncMock

from auditluma.analyzers.impact_assessor import (
    ImpactAssessor, ImpactLevel, PropagationVector, ImpactNode, 
    PropagationPath, ImpactAssessment, RiskCalculator
)
from auditluma.models.code import VulnerabilityResult, SourceFile, CodeUnit
from auditluma.models.hierarchical_rag import ImpactScope


class TestRiskCalculator:
    """风险计算器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.calculator = RiskCalculator()
        self.vulnerability = VulnerabilityResult(
            vulnerability_type="SQL Injection",
            severity="high",
            description="Test vulnerability",
            file_path="test.py",
            line_number=10,
            code_snippet="SELECT * FROM users",
            confidence=0.9
        )
    
    def test_calculate_node_risk_basic(self):
        """测试基本节点风险计算"""
        node = ImpactNode(
            entity_id="test_function",
            entity_type="function",
            impact_level=ImpactLevel.MEDIUM,
            distance_from_source=0,
            propagation_vectors={PropagationVector.FUNCTION_CALL},
            affected_operations={"sql"}
        )
        
        risk = self.calculator.calculate_node_risk(node, self.vulnerability)
        
        assert 0.0 <= risk <= 1.0
        assert risk > 0.5  # 高严重性漏洞应该有较高风险
    
    def test_calculate_node_risk_distance_decay(self):
        """测试距离衰减效应"""
        node1 = ImpactNode(
            entity_id="test_function1",
            entity_type="function",
            impact_level=ImpactLevel.MEDIUM,
            distance_from_source=0,
            propagation_vectors={PropagationVector.FUNCTION_CALL},
            affected_operations=set()
        )
        
        node2 = ImpactNode(
            entity_id="test_function2",
            entity_type="function",
            impact_level=ImpactLevel.MEDIUM,
            distance_from_source=3,
            propagation_vectors={PropagationVector.FUNCTION_CALL},
            affected_operations=set()
        )
        
        risk1 = self.calculator.calculate_node_risk(node1, self.vulnerability)
        risk2 = self.calculator.calculate_node_risk(node2, self.vulnerability)
        
        assert risk1 > risk2  # 距离越远风险越低
    
    def test_calculate_path_risk(self):
        """测试路径风险计算"""
        path = PropagationPath(
            source_node="source",
            target_node="target",
            path_nodes=["source", "middle", "target"],
            propagation_vector=PropagationVector.DATA_FLOW,
            path_length=3,
            risk_score=0.0,
            confidence=0.8
        )
        
        risk = self.calculator.calculate_path_risk(path, self.vulnerability)
        
        assert 0.0 <= risk <= 1.0
    
    def test_get_base_risk_severity_mapping(self):
        """测试严重性映射"""
        # 测试不同严重性级别
        severities = ['critical', 'high', 'medium', 'low', 'info']
        expected_order = []
        
        for severity in severities:
            vuln = VulnerabilityResult(
                vulnerability_type="Test",
                severity=severity,
                description="Test",
                file_path="test.py",
                line_number=1,
                code_snippet="test",
                confidence=0.9
            )
            risk = self.calculator._get_base_risk(vuln)
            expected_order.append(risk)
        
        # 验证风险递减
        for i in range(len(expected_order) - 1):
            assert expected_order[i] >= expected_order[i + 1]
    
    def test_get_entity_type_factor(self):
        """测试实体类型因子"""
        factors = {}
        entity_types = ['function', 'method', 'class', 'module', 'file', 'package']
        
        for entity_type in entity_types:
            factor = self.calculator._get_entity_type_factor(entity_type)
            factors[entity_type] = factor
            assert 0.0 < factor <= 1.0
        
        # 函数和方法应该有最高的因子
        assert factors['function'] == 1.0
        assert factors['method'] == 1.0
    
    def test_get_operation_factor(self):
        """测试操作因子"""
        # 测试高风险操作
        high_risk_ops = {'sql', 'exec', 'eval'}
        factor_high = self.calculator._get_operation_factor(high_risk_ops)
        
        # 测试中等风险操作
        medium_risk_ops = {'read', 'open'}
        factor_medium = self.calculator._get_operation_factor(medium_risk_ops)
        
        # 测试无风险操作
        no_risk_ops = {'print', 'log'}
        factor_none = self.calculator._get_operation_factor(no_risk_ops)
        
        assert factor_high > factor_medium > factor_none


class TestImpactAssessor:
    """影响面评估器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.assessor = ImpactAssessor()
        self.vulnerability = VulnerabilityResult(
            vulnerability_type="SQL Injection",
            severity="high",
            description="Test SQL injection",
            file_path="app.py",
            line_number=25,
            code_snippet="query = f'SELECT * FROM users WHERE id = {user_id}'",
            confidence=0.9
        )
        
        self.source_files = [
            SourceFile(
                path="app.py",
                content="""
def get_user(user_id):
    query = f'SELECT * FROM users WHERE id = {user_id}'
    return execute_query(query)

def execute_query(sql):
    return database.execute(sql)
""",
                file_type="python",
                relative_path="app.py"
            )
        ]
    
    @pytest.mark.asyncio
    async def test_assess_impact_basic(self):
        """测试基本影响评估"""
        # Mock调用图构建器
        mock_call_graph = nx.DiGraph()
        mock_call_graph.add_node("app.py::get_user", code_unit=Mock(
            source_file=self.source_files[0],
            start_line=2,
            end_line=4,
            type="function",
            content="def get_user(user_id):\n    query = f'SELECT * FROM users WHERE id = {user_id}'\n    return execute_query(query)"
        ))
        mock_call_graph.add_node("app.py::execute_query", code_unit=Mock(
            source_file=self.source_files[0],
            start_line=6,
            end_line=7,
            type="function",
            content="def execute_query(sql):\n    return database.execute(sql)"
        ))
        mock_call_graph.add_edge("app.py::get_user", "app.py::execute_query")
        
        with patch.object(self.assessor.call_graph_builder, 'build_call_graph', 
                         new_callable=AsyncMock, return_value=mock_call_graph):
            
            assessment = await self.assessor.assess_impact(self.vulnerability, self.source_files)
            
            # 验证评估结果
            assert isinstance(assessment, ImpactAssessment)
            assert assessment.vulnerability == self.vulnerability
            assert isinstance(assessment.impact_scope, ImpactScope)
            assert len(assessment.impact_nodes) > 0
            assert len(assessment.mitigation_strategies) > 0
    
    @pytest.mark.asyncio
    async def test_assess_impact_with_call_graph(self):
        """测试使用预构建调用图的影响评估"""
        # 创建测试调用图
        call_graph = nx.DiGraph()
        call_graph.add_node("test_function", code_unit=Mock(
            source_file=self.source_files[0],
            start_line=20,
            end_line=30,
            type="function",
            content="test content"
        ))
        
        assessment = await self.assessor.assess_impact(
            self.vulnerability, self.source_files, call_graph
        )
        
        assert isinstance(assessment, ImpactAssessment)
        assert not assessment.assessment_metadata.get('fallback', False)
    
    @pytest.mark.asyncio
    async def test_assess_impact_fallback(self):
        """测试回退评估"""
        # Mock调用图构建器抛出异常
        with patch.object(self.assessor.call_graph_builder, 'build_call_graph', 
                         side_effect=Exception("Test error")):
            
            assessment = await self.assessor.assess_impact(self.vulnerability, self.source_files)
            
            # 验证回退结果
            assert isinstance(assessment, ImpactAssessment)
            assert assessment.assessment_metadata.get('fallback', False)
            assert len(assessment.impact_nodes) == 1
            assert len(assessment.mitigation_strategies) > 0
    
    def test_calculate_impact_scope(self):
        """测试影响范围计算"""
        # 创建测试调用图
        call_graph = nx.DiGraph()
        call_graph.add_node("app.py::get_user", code_unit=Mock(
            source_file=self.source_files[0],
            start_line=2,
            end_line=4,
            type="function"
        ))
        call_graph.add_node("app.py::execute_query", code_unit=Mock(
            source_file=Mock(path="db.py"),
            start_line=10,
            end_line=12,
            type="function"
        ))
        call_graph.add_edge("app.py::get_user", "app.py::execute_query")
        
        impact_scope = self.assessor._calculate_impact_scope(
            self.vulnerability, self.source_files, call_graph
        )
        
        assert isinstance(impact_scope, ImpactScope)
        assert len(impact_scope.affected_files) >= 1
        assert self.vulnerability.file_path in impact_scope.affected_files
        assert 0.0 <= impact_scope.impact_score <= 1.0
        assert impact_scope.criticality_level in ["low", "medium", "high", "critical"]
    
    def test_analyze_propagation_paths(self):
        """测试传播路径分析"""
        # 创建测试调用图
        call_graph = nx.DiGraph()
        call_graph.add_node("app.py::get_user", code_unit=Mock(
            source_file=self.source_files[0],
            start_line=2,
            end_line=4,
            type="function"
        ))
        call_graph.add_node("app.py::execute_query", code_unit=Mock(
            source_file=self.source_files[0],
            start_line=6,
            end_line=7,
            type="function"
        ))
        call_graph.add_node("app.py::caller", code_unit=Mock(
            source_file=self.source_files[0],
            start_line=10,
            end_line=12,
            type="function"
        ))
        call_graph.add_edge("app.py::caller", "app.py::get_user")
        call_graph.add_edge("app.py::get_user", "app.py::execute_query")
        
        paths = self.assessor._analyze_propagation_paths(self.vulnerability, call_graph)
        
        assert isinstance(paths, list)
        for path in paths:
            assert isinstance(path, PropagationPath)
            assert len(path.path_nodes) >= 2
            assert 0.0 <= path.risk_score <= 1.0
            assert 0.0 <= path.confidence <= 1.0
    
    def test_build_impact_nodes(self):
        """测试影响节点构建"""
        # 创建测试调用图
        call_graph = nx.DiGraph()
        call_graph.add_node("test_node", code_unit=Mock(
            source_file=self.source_files[0],
            start_line=20,
            end_line=25,
            type="function",
            content="def test(): sql_query = 'SELECT * FROM table'"
        ))
        
        # 创建测试传播路径
        paths = [
            PropagationPath(
                source_node="source",
                target_node="test_node",
                path_nodes=["source", "test_node"],
                propagation_vector=PropagationVector.FUNCTION_CALL,
                path_length=2,
                risk_score=0.8,
                confidence=0.9
            )
        ]
        
        impact_nodes = self.assessor._build_impact_nodes(self.vulnerability, call_graph, paths)
        
        assert isinstance(impact_nodes, dict)
        assert "test_node" in impact_nodes
        
        node = impact_nodes["test_node"]
        assert isinstance(node, ImpactNode)
        assert isinstance(node.impact_level, ImpactLevel)
        assert isinstance(node.propagation_vectors, set)
        assert 'overall_risk' in node.risk_factors
    
    def test_build_risk_matrix(self):
        """测试风险矩阵构建"""
        # 创建测试影响节点
        impact_nodes = {
            "node1": ImpactNode(
                entity_id="node1",
                entity_type="function",
                impact_level=ImpactLevel.HIGH,
                distance_from_source=0
            ),
            "node2": ImpactNode(
                entity_id="node2",
                entity_type="function",
                impact_level=ImpactLevel.MEDIUM,
                distance_from_source=1
            )
        }
        
        # 创建测试传播路径
        paths = [
            PropagationPath(
                source_node="node1",
                target_node="node2",
                path_nodes=["node1", "node2"],
                propagation_vector=PropagationVector.FUNCTION_CALL,
                path_length=2,
                risk_score=0.7,
                confidence=0.8
            )
        ]
        
        risk_matrix = self.assessor._build_risk_matrix(impact_nodes, paths)
        
        assert isinstance(risk_matrix, dict)
        assert "node1" in risk_matrix
        assert "node2" in risk_matrix["node1"]
        assert risk_matrix["node1"]["node1"] == 1.0  # 自身风险
        assert 0.0 <= risk_matrix["node1"]["node2"] <= 1.0
    
    def test_generate_mitigation_strategies(self):
        """测试缓解策略生成"""
        # 创建测试数据
        impact_nodes = {
            "high_risk_node": ImpactNode(
                entity_id="high_risk_node",
                entity_type="function",
                impact_level=ImpactLevel.HIGH,
                distance_from_source=0
            )
        }
        
        paths = [
            PropagationPath(
                source_node="source",
                target_node="target",
                path_nodes=["source", "middle", "target"],
                propagation_vector=PropagationVector.DATA_FLOW,
                path_length=3,
                risk_score=0.8,
                confidence=0.9
            )
        ]
        
        strategies = self.assessor._generate_mitigation_strategies(
            self.vulnerability, impact_nodes, paths
        )
        
        assert isinstance(strategies, list)
        assert len(strategies) > 0
        
        # 验证SQL注入相关策略
        sql_strategies = [s for s in strategies if 'injection' in s.lower() or 'sql' in s.lower()]
        assert len(sql_strategies) > 0
    
    def test_generate_impact_report(self):
        """测试影响报告生成"""
        # 创建测试评估结果
        impact_scope = ImpactScope(
            affected_files=["app.py", "db.py"],
            affected_functions=["get_user", "execute_query"],
            risk_propagation_paths=[],
            impact_score=0.7,
            criticality_level="high"
        )
        
        impact_nodes = {
            "node1": ImpactNode(
                entity_id="node1",
                entity_type="function",
                impact_level=ImpactLevel.HIGH,
                distance_from_source=0,
                risk_factors={'overall_risk': 0.8}
            ),
            "node2": ImpactNode(
                entity_id="node2",
                entity_type="function",
                impact_level=ImpactLevel.MEDIUM,
                distance_from_source=1,
                risk_factors={'overall_risk': 0.6}
            )
        }
        
        paths = [
            PropagationPath(
                source_node="node1",
                target_node="node2",
                path_nodes=["node1", "node2"],
                propagation_vector=PropagationVector.FUNCTION_CALL,
                path_length=2,
                risk_score=0.7,
                confidence=0.8
            )
        ]
        
        assessment = ImpactAssessment(
            vulnerability=self.vulnerability,
            impact_scope=impact_scope,
            impact_nodes=impact_nodes,
            propagation_paths=paths,
            risk_matrix={},
            mitigation_strategies=["Strategy 1", "Strategy 2"],
            assessment_metadata={'test': True}
        )
        
        report = self.assessor.generate_impact_report(assessment)
        
        # 验证报告结构
        assert isinstance(report, dict)
        assert "vulnerability_info" in report
        assert "impact_summary" in report
        assert "propagation_analysis" in report
        assert "risk_distribution" in report
        assert "mitigation_strategies" in report
        assert "metadata" in report
        
        # 验证具体内容
        assert report["vulnerability_info"]["type"] == "SQL Injection"
        assert report["impact_summary"]["total_affected_nodes"] == 2
        assert report["impact_summary"]["critical_impact_nodes"] == 0
        assert report["impact_summary"]["high_impact_nodes"] == 1
        assert report["propagation_analysis"]["total_paths"] == 1


class TestImpactNode:
    """影响节点测试"""
    
    def test_impact_node_creation(self):
        """测试影响节点创建"""
        node = ImpactNode(
            entity_id="test_function",
            entity_type="function",
            impact_level=ImpactLevel.HIGH,
            distance_from_source=1,
            propagation_vectors={PropagationVector.FUNCTION_CALL, PropagationVector.DATA_FLOW},
            affected_operations={"sql", "query"},
            risk_factors={"overall_risk": 0.8},
            metadata={"test": "value"}
        )
        
        assert node.entity_id == "test_function"
        assert node.entity_type == "function"
        assert node.impact_level == ImpactLevel.HIGH
        assert node.distance_from_source == 1
        assert len(node.propagation_vectors) == 2
        assert len(node.affected_operations) == 2
        assert node.risk_factors["overall_risk"] == 0.8
        assert node.metadata["test"] == "value"


class TestPropagationPath:
    """传播路径测试"""
    
    def test_propagation_path_creation(self):
        """测试传播路径创建"""
        path = PropagationPath(
            source_node="source",
            target_node="target",
            path_nodes=["source", "middle", "target"],
            propagation_vector=PropagationVector.DATA_FLOW,
            path_length=3,
            risk_score=0.7,
            confidence=0.8,
            critical_points=["middle"],
            mitigation_points=["sanitizer"]
        )
        
        assert path.source_node == "source"
        assert path.target_node == "target"
        assert len(path.path_nodes) == 3
        assert path.propagation_vector == PropagationVector.DATA_FLOW
        assert path.path_length == 3
        assert path.risk_score == 0.7
        assert path.confidence == 0.8
        assert len(path.critical_points) == 1
        assert len(path.mitigation_points) == 1


class TestImpactAssessment:
    """影响评估结果测试"""
    
    def test_impact_assessment_creation(self):
        """测试影响评估结果创建"""
        vulnerability = VulnerabilityResult(
            vulnerability_type="Test",
            severity="high",
            description="Test vulnerability",
            file_path="test.py",
            line_number=1,
            code_snippet="test",
            confidence=0.9
        )
        
        impact_scope = ImpactScope(
            affected_files=["test.py"],
            affected_functions=["test_function"],
            risk_propagation_paths=[],
            impact_score=0.5,
            criticality_level="medium"
        )
        
        assessment = ImpactAssessment(
            vulnerability=vulnerability,
            impact_scope=impact_scope,
            impact_nodes={},
            propagation_paths=[],
            risk_matrix={},
            mitigation_strategies=["Strategy 1"],
            assessment_metadata={"test": True}
        )
        
        assert assessment.vulnerability == vulnerability
        assert assessment.impact_scope == impact_scope
        assert isinstance(assessment.impact_nodes, dict)
        assert isinstance(assessment.propagation_paths, list)
        assert isinstance(assessment.risk_matrix, dict)
        assert len(assessment.mitigation_strategies) == 1
        assert assessment.assessment_metadata["test"] is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])