"""
语义上下文扩展器单元测试
"""

import pytest
import asyncio
from pathlib import Path

from auditluma.models.code import VulnerabilityResult, SourceFile, CodeUnit, FileType
from auditluma.analyzers.context_expander import ContextExpander, ContextWindow, SemanticRelation


class TestContextExpander:
    """语义上下文扩展器测试类"""
    
    @pytest.fixture
    def sample_source_file(self):
        """创建示例源文件"""
        content = """
import sqlite3
import hashlib

def authenticate_user(username, password):
    # 连接数据库
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    # 不安全的查询 - SQL注入风险
    query = f"SELECT * FROM users WHERE username = '{username}' AND password = '{password}'"
    cursor.execute(query)
    result = cursor.fetchone()
    
    cursor.close()
    conn.close()
    
    return result is not None

def hash_password(password):
    # 密码哈希
    return hashlib.sha256(password.encode()).hexdigest()

def validate_input(user_input):
    # 输入验证
    if not user_input or len(user_input) > 100:
        return False
    
    # 检查危险字符
    dangerous_chars = ["'", '"', ';', '--', '/*', '*/']
    for char in dangerous_chars:
        if char in user_input:
            return False
    
    return True

def safe_authenticate_user(username, password):
    # 安全的用户认证
    if not validate_input(username) or not validate_input(password):
        return False
    
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    # 使用参数化查询
    cursor.execute("SELECT password_hash FROM users WHERE username = ?", (username,))
    result = cursor.fetchone()
    
    if result:
        stored_hash = result[0]
        input_hash = hash_password(password)
        return stored_hash == input_hash
    
    cursor.close()
    conn.close()
    
    return False
"""
        
        return SourceFile(
            path=Path("auth.py"),
            relative_path="auth.py",
            name="auth.py",
            extension=".py",
            file_type=FileType.PYTHON,
            size=len(content),
            content=content,
            modified_time=0.0
        )
    
    @pytest.fixture
    def sample_vulnerability(self, sample_source_file):
        """创建示例漏洞"""
        code_unit = CodeUnit(
            id="authenticate_user",
            name="authenticate_user",
            type="function",
            source_file=sample_source_file,
            start_line=5,
            end_line=18,
            content="def authenticate_user(username, password):\n    # function content..."
        )
        
        return VulnerabilityResult(
            id="sql_injection_001",
            title="SQL Injection in Authentication",
            description="SQL injection vulnerability in user authentication",
            code_unit=code_unit,
            file_path="auth.py",
            start_line=11,
            end_line=11,
            vulnerability_type="SQL Injection",
            severity="high",
            snippet="query = f\"SELECT * FROM users WHERE username = '{username}' AND password = '{password}'\"",
            confidence=0.9
        )
    
    @pytest.fixture
    def context_expander(self):
        """创建上下文扩展器实例"""
        return ContextExpander({
            'max_window_size': 50,
            'min_relevance_threshold': 0.2,
            'expansion_strategies': [
                'variable_tracking',
                'function_boundary',
                'control_flow',
                'data_dependency',
                'semantic_similarity'
            ]
        })
    
    @pytest.mark.asyncio
    async def test_expand_context_basic(self, context_expander, sample_vulnerability, sample_source_file):
        """测试基本上下文扩展功能"""
        expanded_context = await context_expander.expand_context(
            vulnerability=sample_vulnerability,
            source_file=sample_source_file
        )
        
        # 验证基本属性
        assert expanded_context is not None
        assert len(expanded_context.original_context) > 0
        assert len(expanded_context.expanded_content) >= len(expanded_context.original_context)
        assert len(expanded_context.context_windows) > 0
        assert 0.0 <= expanded_context.completeness_score <= 1.0
        
        # 验证至少有原始上下文窗口
        original_windows = [w for w in expanded_context.context_windows 
                          if w.expansion_reason == "original_context"]
        assert len(original_windows) >= 1
    
    @pytest.mark.asyncio
    async def test_variable_tracking_expansion(self, context_expander, sample_vulnerability, sample_source_file):
        """测试变量追踪扩展"""
        expanded_context = await context_expander.expand_context(
            vulnerability=sample_vulnerability,
            source_file=sample_source_file
        )
        
        # 检查是否有变量追踪窗口
        variable_windows = [w for w in expanded_context.context_windows 
                          if "variable_tracking" in w.expansion_reason]
        assert len(variable_windows) > 0
        
        # 验证变量追踪窗口的相关性
        for window in variable_windows:
            assert window.relevance_score > 0.0
            assert len(window.content) > 0
    
    @pytest.mark.asyncio
    async def test_function_boundary_expansion(self, context_expander, sample_vulnerability, sample_source_file):
        """测试函数边界扩展"""
        expanded_context = await context_expander.expand_context(
            vulnerability=sample_vulnerability,
            source_file=sample_source_file
        )
        
        # 检查是否有函数边界窗口
        function_windows = [w for w in expanded_context.context_windows 
                          if w.expansion_reason == "function_boundary"]
        assert len(function_windows) > 0
        
        # 验证函数边界窗口包含完整函数
        for window in function_windows:
            assert "def " in window.content or "function " in window.content
    
    @pytest.mark.asyncio
    async def test_semantic_similarity_expansion(self, context_expander, sample_vulnerability, sample_source_file):
        """测试语义相似性扩展"""
        expanded_context = await context_expander.expand_context(
            vulnerability=sample_vulnerability,
            source_file=sample_source_file
        )
        
        # 检查是否有语义相似性窗口
        semantic_windows = [w for w in expanded_context.context_windows 
                          if w.expansion_reason == "semantic_similarity"]
        
        # 验证语义相似性窗口的质量
        for window in semantic_windows:
            assert window.relevance_score >= context_expander.min_relevance_threshold
    
    @pytest.mark.asyncio
    async def test_semantic_relations_analysis(self, context_expander, sample_vulnerability, sample_source_file):
        """测试语义关系分析"""
        expanded_context = await context_expander.expand_context(
            vulnerability=sample_vulnerability,
            source_file=sample_source_file
        )
        
        # 验证语义关系
        assert len(expanded_context.semantic_relations) > 0
        
        # 检查关系类型
        relation_types = {r.relation_type for r in expanded_context.semantic_relations}
        expected_types = {"variable_assignment", "function_call", "data_dependency_assignment"}
        
        # 至少应该有一些预期的关系类型
        assert len(relation_types.intersection(expected_types)) > 0
        
        # 验证关系强度
        for relation in expanded_context.semantic_relations:
            assert 0.0 <= relation.strength <= 1.0
            assert relation.source_entity != relation.target_entity
    
    @pytest.mark.asyncio
    async def test_completeness_score_calculation(self, context_expander, sample_vulnerability, sample_source_file):
        """测试完整性评分计算"""
        expanded_context = await context_expander.expand_context(
            vulnerability=sample_vulnerability,
            source_file=sample_source_file
        )
        
        # 验证完整性评分在合理范围内
        assert 0.0 <= expanded_context.completeness_score <= 1.0
        
        # 有更多窗口和关系的上下文应该有更高的完整性评分
        assert expanded_context.completeness_score > 0.3  # 基础分数
    
    @pytest.mark.asyncio
    async def test_create_semantic_context(self, context_expander, sample_vulnerability, sample_source_file):
        """测试语义上下文对象创建"""
        expanded_context = await context_expander.expand_context(
            vulnerability=sample_vulnerability,
            source_file=sample_source_file
        )
        
        semantic_context = context_expander.create_semantic_context(expanded_context)
        
        # 验证语义上下文对象
        assert semantic_context is not None
        assert len(semantic_context.related_code_blocks) == len(expanded_context.context_windows)
        assert semantic_context.context_window_size == len(expanded_context.expanded_content)
        assert semantic_context.expanded_context == expanded_context.expanded_content
        assert len(semantic_context.semantic_similarity_scores) > 0
    
    @pytest.mark.asyncio
    async def test_error_handling(self, context_expander):
        """测试错误处理"""
        # 创建无效的漏洞和源文件
        invalid_source_file = SourceFile(
            path=Path("invalid.py"),
            relative_path="invalid.py",
            name="invalid.py",
            extension=".py",
            file_type=FileType.PYTHON,
            size=0,
            content="",  # 空内容
            modified_time=0.0
        )
        
        code_unit = CodeUnit(
            id="invalid",
            name="invalid",
            type="function",
            source_file=invalid_source_file,
            start_line=1,
            end_line=1,
            content=""
        )
        
        invalid_vulnerability = VulnerabilityResult(
            id="invalid",
            title="Invalid",
            description="Invalid vulnerability",
            code_unit=code_unit,
            file_path="invalid.py",
            start_line=1,
            end_line=1,
            vulnerability_type="Unknown",
            severity="low",
            snippet="",
            confidence=0.1
        )
        
        # 应该返回回退上下文而不是抛出异常
        expanded_context = await context_expander.expand_context(
            vulnerability=invalid_vulnerability,
            source_file=invalid_source_file
        )
        
        assert expanded_context is not None
        # 对于空内容，应该至少有一个基础窗口
        assert len(expanded_context.context_windows) >= 1
        # 完整性评分应该较低
        assert expanded_context.completeness_score < 0.5
    
    def test_extract_variables_from_snippet(self, context_expander):
        """测试从代码片段提取变量"""
        snippet = "username = input('Enter username: ')\npassword = get_password(username)\nresult = authenticate(username, password)"
        
        variables = context_expander._extract_variables_from_snippet(snippet)
        
        expected_variables = {'username', 'password', 'result', 'input', 'get_password', 'authenticate'}
        assert variables.intersection(expected_variables)
    
    def test_calculate_keyword_similarity(self, context_expander):
        """测试关键词相似度计算"""
        keywords1 = {'username', 'password', 'database', 'query'}
        keywords2 = {'username', 'password', 'authentication', 'security'}
        
        similarity = context_expander._calculate_keyword_similarity(keywords1, keywords2)
        
        assert 0.0 <= similarity <= 1.0
        assert similarity > 0.0  # 应该有一些相似性
    
    def test_find_function_boundaries(self, context_expander, sample_source_file):
        """测试函数边界查找"""
        lines = sample_source_file.content.split('\n')
        
        # 查找authenticate_user函数的边界
        start, end = context_expander._find_function_boundaries(10, lines)
        
        assert start is not None
        assert end is not None
        assert start < end
        assert "def authenticate_user" in lines[start]
    
    def test_deduplicate_windows(self, context_expander):
        """测试窗口去重"""
        # 创建重复的窗口
        window1 = ContextWindow(
            center_line=10,
            start_line=5,
            end_line=15,
            content="test content",
            relevance_score=0.8,
            expansion_reason="test1"
        )
        
        window2 = ContextWindow(
            center_line=12,
            start_line=5,
            end_line=15,  # 相同范围
            content="test content 2",
            relevance_score=0.9,
            expansion_reason="test2"
        )
        
        window3 = ContextWindow(
            center_line=20,
            start_line=18,
            end_line=22,  # 不同范围
            content="different content",
            relevance_score=0.7,
            expansion_reason="test3"
        )
        
        windows = [window1, window2, window3]
        unique_windows = context_expander._deduplicate_windows(windows)
        
        # 应该去除重复的窗口
        assert len(unique_windows) == 2
        
        # 验证保留的窗口
        ranges = {(w.start_line, w.end_line) for w in unique_windows}
        assert (5, 15) in ranges
        assert (18, 22) in ranges


if __name__ == "__main__":
    pytest.main([__file__, "-v"])