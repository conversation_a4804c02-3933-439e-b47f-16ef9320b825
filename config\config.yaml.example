# AuditLuma 配置文件示例
# 复制此文件为 config.yaml 并根据需要修改

# 全局设置
global:
  show_thinking: false
  language: "zh-CN"
  target_dir: "./goalfile"
  report_dir: "./reports"
  report_format: "html"

# OpenAI 配置
openai:
  model: "gpt-4-turbo-preview"
  api_key: "${OPENAI_API_KEY}"  # 从环境变量读取
  base_url: "https://api.openai.com/v1"
  max_tokens: 8000
  temperature: 0.1

# DeepSeek 配置
deepseek:
  model: "deepseek-chat"
  api_key: "${DEEPSEEK_API_KEY}"
  base_url: "https://api.deepseek.com/v1"
  max_tokens: 8000
  temperature: 0.1

# 代理设置
agent:
  default_provider: "openai"
  system_prompt: "你是一个专业的代码安全审计助手"
  memory_limit: 10

# 项目配置
project:
  name: "AuditLuma项目"
  max_file_size: 1000000
  max_batch_size: 20
  ignored_extensions: [".jpg", ".png", ".gif", ".mp3", ".mp4", ".zip"]
  ignored_directories: ["node_modules", "__pycache__", ".git", "dist", "build"]

# Self-RAG 配置
self_rag:
  enabled: true
  vector_store: "faiss"
  embedding_model: "text-embedding-ada-002@openai"
  chunk_size: 1000
  chunk_overlap: 200
  max_documents: 10000
  retrieval_k: 5
  relevance_threshold: 0.75

# 🤖 层级RAG架构模型配置（集中管理所有模型）
hierarchical_rag_models:
  enabled: true
  
  # Haystack编排层模型配置
  haystack:
    default_model: "gpt-3.5-turbo@openai"  # 支持 "model@provider" 格式
    task_models:
      security_scan: "gpt-4@openai"        # 安全扫描使用更强的模型
      syntax_check: "gpt-3.5-turbo@openai"
      logic_analysis: "gpt-3.5-turbo@openai"
      dependency_analysis: "gpt-3.5-turbo@openai"
  
  # txtai知识检索层模型配置
  txtai:
    retrieval_model: "gpt-3.5-turbo@openai"
    embedding_model: "text-embedding-ada-002@openai"
  
  # R2R上下文增强层模型配置
  r2r:
    context_model: "gpt-3.5-turbo@openai"
    enhancement_model: "gpt-3.5-turbo@openai"
  
  # Self-RAG验证层模型配置
  self_rag_validation:
    validation_model: "gpt-3.5-turbo@openai"
    cross_validation_models:
      - "gpt-4@openai"
      - "deepseek-chat@deepseek"
      - "gpt-3.5-turbo@openai"

# ⚙️ 层级RAG架构详细配置（其他配置分开管理）
hierarchical_rag:
  enabled: true
  architecture_mode: "hierarchical"
  
  # Haystack编排层配置
  haystack:
    enabled: true
    use_haystack_ai: true  # 使用官方Haystack-AI库
    max_workers: 10
    task_timeout: 300
    retry_attempts: 3
    enable_fallback: true
  
  # txtai知识检索层配置
  txtai:
    enabled: true
    cve_cache_ttl: 3600
    similarity_threshold: 0.8
    retrieval_timeout: 30
  
  # R2R上下文增强层配置
  r2r:
    enabled: true
    max_call_depth: 10
    enable_cross_file_analysis: true
    enable_data_flow_analysis: true
  
  # Self-RAG验证层配置
  self_rag_validation:
    enabled: true
    confidence_threshold: 0.7
    false_positive_filter_enabled: true
    validation_timeout: 60

# 多智能体协作协议 (MCP)
mcp:
  enabled: true
  agents:
    - name: "code_analyzer"
      description: "代码分析智能体"
      type: "analyzer"
      priority: 1
      model: "gpt-3.5-turbo@openai"
    
    - name: "security_analyst"
      description: "安全分析智能体"
      type: "analyst"
      priority: 2
      model: "gpt-4@openai"

# 工具设置
tools:
  enabled: ["code_analyzer", "security_scanner", "dependency_analyzer"]

# UI设置
ui:
  theme: "blue"
  use_colors: true
  verbosity: "normal"

# 默认模型配置
default_models:
  code_analysis: "gpt-4-turbo-preview"
  security_audit: "gpt-4-turbo-preview"
  remediation: "gpt-4-turbo-preview"
  summarization: "gpt-3.5-turbo"

# 输出配置
output:
  formats: ["html", "json", "markdown"]
  visualization: true
  max_results: 100
  severity_levels: ["critical", "high", "medium", "low", "info"]

# 漏洞数据库
vulnerability_db:
  sources: ["OWASP Top 10", "CWE Top 25", "SANS Top 25"]
  update_frequency: "weekly"
  local_storage: "./data/vulnerability_db"