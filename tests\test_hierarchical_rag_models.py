"""
层级RAG数据模型单元测试
"""

import pytest
import json
from datetime import datetime
from pathlib import Path

from auditluma.models.hierarchical_rag import (
    AuditTask, TaskResult, TaskType, TaskStatus, ValidationStatus,
    CVEInfo, BestPractice, HistoricalCase, VulnerabilityKnowledge,
    CallChain, DataFlowInfo, ImpactScope, SemanticContext, EnhancedContext,
    ConfidenceScore, ValidationSummary, ValidatedVulnerability, ValidatedResults,
    AuditResult, TaskCollection
)
from auditluma.models.code import SourceFile, FileType, VulnerabilityResult, SeverityLevel, CodeUnit


class TestAuditTask:
    """测试AuditTask数据模型"""
    
    def test_audit_task_creation(self):
        """测试审计任务创建"""
        source_file = SourceFile(
            path=Path("test.py"),
            relative_path="test.py",
            name="test.py",
            extension=".py",
            file_type=FileType.PYTHON,
            size=100,
            content="print('hello')",
            modified_time=1234567890.0
        )
        
        task = AuditTask(
            id="task-1",
            type=TaskType.SYNTAX_CHECK,
            priority=1,
            source_files=[source_file],
            dependencies=["task-0"],
            timeout=300
        )
        
        assert task.id == "task-1"
        assert task.type == TaskType.SYNTAX_CHECK
        assert task.priority == 1
        assert len(task.source_files) == 1
        assert task.dependencies == ["task-0"]
        assert task.timeout == 300
        assert task.status == TaskStatus.PENDING
        assert task.duration is None
    
    def test_audit_task_serialization(self):
        """测试审计任务序列化"""
        source_file = SourceFile(
            path=Path("test.py"),
            relative_path="test.py",
            name="test.py",
            extension=".py",
            file_type=FileType.PYTHON,
            size=100,
            content="print('hello')",
            modified_time=1234567890.0
        )
        
        task = AuditTask(
            id="task-1",
            type=TaskType.SYNTAX_CHECK,
            priority=1,
            source_files=[source_file]
        )
        
        task_dict = task.to_dict()
        
        assert task_dict['id'] == "task-1"
        assert task_dict['type'] == "syntax_check"
        assert task_dict['priority'] == 1
        assert task_dict['source_files'] == ["test.py"]
        assert task_dict['status'] == "pending"
    
    def test_audit_task_duration_calculation(self):
        """测试任务执行时长计算"""
        source_file = SourceFile(
            path=Path("test.py"),
            relative_path="test.py",
            name="test.py",
            extension=".py",
            file_type=FileType.PYTHON,
            size=100,
            content="print('hello')",
            modified_time=1234567890.0
        )
        
        task = AuditTask(
            id="task-1",
            type=TaskType.SYNTAX_CHECK,
            priority=1,
            source_files=[source_file]
        )
        
        # 设置开始和结束时间
        start_time = datetime(2023, 1, 1, 10, 0, 0)
        end_time = datetime(2023, 1, 1, 10, 0, 5)  # 5秒后
        
        task.started_at = start_time
        task.completed_at = end_time
        
        assert task.duration == 5.0


class TestCVEInfo:
    """测试CVEInfo数据模型"""
    
    def test_cve_info_creation(self):
        """测试CVE信息创建"""
        cve = CVEInfo(
            cve_id="CVE-2023-1234",
            description="Test vulnerability",
            severity="HIGH",
            cvss_score=8.5,
            published_date=datetime(2023, 1, 1),
            modified_date=datetime(2023, 1, 2),
            references=["https://example.com"],
            affected_products=["Product A"],
            cwe_ids=["CWE-79"]
        )
        
        assert cve.cve_id == "CVE-2023-1234"
        assert cve.severity == "HIGH"
        assert cve.cvss_score == 8.5
        assert len(cve.references) == 1
        assert len(cve.affected_products) == 1
        assert len(cve.cwe_ids) == 1
    
    def test_cve_info_serialization(self):
        """测试CVE信息序列化和反序列化"""
        cve = CVEInfo(
            cve_id="CVE-2023-1234",
            description="Test vulnerability",
            severity="HIGH",
            cvss_score=8.5,
            published_date=datetime(2023, 1, 1),
            modified_date=datetime(2023, 1, 2)
        )
        
        cve_dict = cve.to_dict()
        cve_restored = CVEInfo.from_dict(cve_dict)
        
        assert cve_restored.cve_id == cve.cve_id
        assert cve_restored.severity == cve.severity
        assert cve_restored.cvss_score == cve.cvss_score
        assert cve_restored.published_date == cve.published_date
        assert cve_restored.modified_date == cve.modified_date


class TestVulnerabilityKnowledge:
    """测试VulnerabilityKnowledge数据模型"""
    
    def test_vulnerability_knowledge_creation(self):
        """测试漏洞知识创建"""
        cve = CVEInfo(
            cve_id="CVE-2023-1234",
            description="Test vulnerability",
            severity="HIGH",
            cvss_score=8.5,
            published_date=datetime(2023, 1, 1),
            modified_date=datetime(2023, 1, 2)
        )
        
        best_practice = BestPractice(
            id="bp-1",
            title="Input Validation",
            description="Always validate input",
            category="security",
            language="python",
            source="OWASP",
            code_pattern="validate_input()",
            recommendation="Use proper validation"
        )
        
        historical_case = HistoricalCase(
            id="hc-1",
            title="SQL Injection Case",
            description="Historical SQL injection",
            code_pattern="SELECT * FROM users WHERE id = " + "user_input",
            vulnerability_type="sql_injection",
            solution="Use parameterized queries",
            similarity_score=0.9,
            case_date=datetime(2022, 1, 1),
            source_project="Project A"
        )
        
        knowledge = VulnerabilityKnowledge(
            cve_info=[cve],
            best_practices=[best_practice],
            historical_cases=[historical_case],
            relevance_scores={"cve": 0.8, "best_practice": 0.9},
            retrieval_time=0.5
        )
        
        assert len(knowledge.cve_info) == 1
        assert len(knowledge.best_practices) == 1
        assert len(knowledge.historical_cases) == 1
        assert knowledge.relevance_scores["cve"] == 0.8
        assert knowledge.retrieval_time == 0.5
    
    def test_vulnerability_knowledge_serialization(self):
        """测试漏洞知识序列化和反序列化"""
        cve = CVEInfo(
            cve_id="CVE-2023-1234",
            description="Test vulnerability",
            severity="HIGH",
            cvss_score=8.5,
            published_date=datetime(2023, 1, 1),
            modified_date=datetime(2023, 1, 2)
        )
        
        knowledge = VulnerabilityKnowledge(
            cve_info=[cve],
            relevance_scores={"cve": 0.8},
            retrieval_time=0.5
        )
        
        knowledge_dict = knowledge.to_dict()
        knowledge_restored = VulnerabilityKnowledge.from_dict(knowledge_dict)
        
        assert len(knowledge_restored.cve_info) == 1
        assert knowledge_restored.cve_info[0].cve_id == "CVE-2023-1234"
        assert knowledge_restored.relevance_scores["cve"] == 0.8
        assert knowledge_restored.retrieval_time == 0.5


class TestEnhancedContext:
    """测试EnhancedContext数据模型"""
    
    def test_enhanced_context_creation(self):
        """测试增强上下文创建"""
        call_chain = CallChain(
            functions=["func1", "func2"],
            call_depth=2,
            cross_file_calls=[{"from": "file1.py", "to": "file2.py"}],
            entry_points=["main"]
        )
        
        data_flow = DataFlowInfo(
            taint_sources=["user_input"],
            taint_sinks=["database_query"],
            data_paths=[["user_input", "process_data", "database_query"]],
            variable_tracking={"var1": ["line1", "line2"]}
        )
        
        impact_scope = ImpactScope(
            affected_files=["file1.py", "file2.py"],
            affected_functions=["func1", "func2"],
            risk_propagation_paths=[["func1", "func2"]],
            impact_score=0.8,
            criticality_level="high"
        )
        
        semantic_context = SemanticContext(
            related_code_blocks=["block1", "block2"],
            semantic_similarity_scores={"block1": 0.9},
            context_window_size=100,
            expanded_context="expanded code context"
        )
        
        enhanced_context = EnhancedContext(
            call_chain=call_chain,
            data_flow=data_flow,
            impact_scope=impact_scope,
            semantic_context=semantic_context,
            completeness_score=0.85,
            enhancement_time=1.2
        )
        
        assert enhanced_context.call_chain.call_depth == 2
        assert len(enhanced_context.data_flow.taint_sources) == 1
        assert enhanced_context.impact_scope.impact_score == 0.8
        assert enhanced_context.semantic_context.context_window_size == 100
        assert enhanced_context.completeness_score == 0.85
        assert enhanced_context.enhancement_time == 1.2
    
    def test_enhanced_context_serialization(self):
        """测试增强上下文序列化和反序列化"""
        enhanced_context = EnhancedContext(
            completeness_score=0.85,
            enhancement_time=1.2
        )
        
        context_dict = enhanced_context.to_dict()
        context_restored = EnhancedContext.from_dict(context_dict)
        
        assert context_restored.completeness_score == 0.85
        assert context_restored.enhancement_time == 1.2


class TestConfidenceScore:
    """测试ConfidenceScore数据模型"""
    
    def test_confidence_score_creation(self):
        """测试置信度评分创建"""
        confidence = ConfidenceScore(
            overall_score=0.85,
            component_scores={
                "code_quality": 0.9,
                "pattern_match": 0.8,
                "context_completeness": 0.85
            },
            explanation="High confidence based on multiple factors",
            factors=["strong pattern match", "complete context", "high code quality"]
        )
        
        assert confidence.overall_score == 0.85
        assert len(confidence.component_scores) == 3
        assert confidence.component_scores["code_quality"] == 0.9
        assert len(confidence.factors) == 3
    
    def test_confidence_score_serialization(self):
        """测试置信度评分序列化和反序列化"""
        confidence = ConfidenceScore(
            overall_score=0.85,
            component_scores={"code_quality": 0.9},
            explanation="Test explanation"
        )
        
        confidence_dict = confidence.to_dict()
        confidence_restored = ConfidenceScore.from_dict(confidence_dict)
        
        assert confidence_restored.overall_score == 0.85
        assert confidence_restored.component_scores["code_quality"] == 0.9
        assert confidence_restored.explanation == "Test explanation"


class TestAuditResult:
    """测试AuditResult数据模型"""
    
    def test_audit_result_creation(self):
        """测试审计结果创建"""
        code_unit = CodeUnit(
            id="unit-1",
            name="test_function",
            type="function",
            source_file=None,  # 简化测试
            start_line=1,
            end_line=10,
            content="def test_function(): pass"
        )
        
        vulnerability = VulnerabilityResult(
            id="vuln-1",
            title="SQL Injection",
            description="Potential SQL injection vulnerability",
            code_unit=code_unit,
            file_path="test.py",
            start_line=5,
            end_line=5,
            vulnerability_type="sql_injection",
            severity=SeverityLevel.HIGH,
            confidence=0.9
        )
        
        task_result = TaskResult(
            task_id="task-1",
            task_type=TaskType.SECURITY_SCAN,
            status=TaskStatus.COMPLETED,
            vulnerabilities=[vulnerability],
            processing_time=2.5
        )
        
        audit_result = AuditResult(
            id="audit-1",
            vulnerabilities=[vulnerability],
            processing_time=5.0,
            confidence_score=0.85,
            task_results=[task_result],
            execution_summary={"total_files": 1, "total_vulnerabilities": 1},
            metadata={"version": "1.0"}
        )
        
        assert audit_result.id == "audit-1"
        assert len(audit_result.vulnerabilities) == 1
        assert audit_result.processing_time == 5.0
        assert audit_result.confidence_score == 0.85
        assert len(audit_result.task_results) == 1
        assert audit_result.execution_summary["total_files"] == 1
    
    def test_audit_result_serialization(self):
        """测试审计结果序列化"""
        audit_result = AuditResult(
            id="audit-1",
            processing_time=5.0,
            confidence_score=0.85,
            execution_summary={"total_files": 1},
            metadata={"version": "1.0"}
        )
        
        result_dict = audit_result.to_dict()
        json_str = audit_result.to_json()
        
        assert result_dict['id'] == "audit-1"
        assert result_dict['processing_time'] == 5.0
        assert result_dict['confidence_score'] == 0.85
        assert isinstance(json_str, str)
        
        # 测试JSON解析
        parsed_data = json.loads(json_str)
        assert parsed_data['id'] == "audit-1"


class TestTaskCollection:
    """测试TaskCollection数据模型"""
    
    def test_task_collection_creation(self):
        """测试任务集合创建"""
        source_file = SourceFile(
            path=Path("test.py"),
            relative_path="test.py",
            name="test.py",
            extension=".py",
            file_type=FileType.PYTHON,
            size=100,
            content="print('hello')",
            modified_time=1234567890.0
        )
        
        syntax_task = AuditTask(
            id="syntax-1",
            type=TaskType.SYNTAX_CHECK,
            priority=1,
            source_files=[source_file]
        )
        
        logic_task = AuditTask(
            id="logic-1",
            type=TaskType.LOGIC_ANALYSIS,
            priority=2,
            source_files=[source_file],
            dependencies=["syntax-1"]
        )
        
        collection = TaskCollection()
        collection.add_task(syntax_task)
        collection.add_task(logic_task)
        
        assert len(collection.tasks) == 2
        assert len(collection.syntax_tasks) == 1
        assert len(collection.logic_tasks) == 1
        assert len(collection.security_tasks) == 0
        assert len(collection.dependency_tasks) == 0
    
    def test_task_collection_ready_tasks(self):
        """测试获取可执行任务"""
        source_file = SourceFile(
            path=Path("test.py"),
            relative_path="test.py",
            name="test.py",
            extension=".py",
            file_type=FileType.PYTHON,
            size=100,
            content="print('hello')",
            modified_time=1234567890.0
        )
        
        syntax_task = AuditTask(
            id="syntax-1",
            type=TaskType.SYNTAX_CHECK,
            priority=1,
            source_files=[source_file]
        )
        
        logic_task = AuditTask(
            id="logic-1",
            type=TaskType.LOGIC_ANALYSIS,
            priority=2,
            source_files=[source_file],
            dependencies=["syntax-1"]
        )
        
        collection = TaskCollection()
        collection.add_task(syntax_task)
        collection.add_task(logic_task)
        
        # 初始状态下，只有语法检查任务可以执行（无依赖）
        ready_tasks = collection.get_ready_tasks()
        assert len(ready_tasks) == 1
        assert ready_tasks[0].id == "syntax-1"
        
        # 完成语法检查任务后，逻辑分析任务变为可执行
        syntax_task.status = TaskStatus.COMPLETED
        ready_tasks = collection.get_ready_tasks()
        assert len(ready_tasks) == 1
        assert ready_tasks[0].id == "logic-1"
    
    def test_task_collection_get_task_by_id(self):
        """测试根据ID获取任务"""
        source_file = SourceFile(
            path=Path("test.py"),
            relative_path="test.py",
            name="test.py",
            extension=".py",
            file_type=FileType.PYTHON,
            size=100,
            content="print('hello')",
            modified_time=1234567890.0
        )
        
        task = AuditTask(
            id="test-task",
            type=TaskType.SYNTAX_CHECK,
            priority=1,
            source_files=[source_file]
        )
        
        collection = TaskCollection()
        collection.add_task(task)
        
        found_task = collection.get_task_by_id("test-task")
        assert found_task is not None
        assert found_task.id == "test-task"
        
        not_found_task = collection.get_task_by_id("non-existent")
        assert not_found_task is None


if __name__ == "__main__":
    pytest.main([__file__])