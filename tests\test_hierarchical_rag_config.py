"""
层级RAG配置系统单元测试
"""

import pytest
import yaml
import tempfile
import os
from pathlib import Path

from auditluma.config import (
    Config, HierarchicalRAGConfig, HaystackConfig, TxtaiConfig, 
    R2RConfig, SelfRAGValidationConfig, CacheConfig, MonitoringConfig, SecurityConfig
)
from auditluma.migration.config_migrator import ConfigMigrator, ConfigHotReloader


class TestHierarchicalRAGConfig:
    """测试层级RAG配置"""
    
    def test_hierarchical_rag_config_creation(self):
        """测试层级RAG配置创建"""
        config = HierarchicalRAGConfig()
        
        assert config.enabled == False  # 默认关闭
        assert config.architecture_mode == "hierarchical"
        assert config.backward_compatibility == True
        assert config.fallback_to_traditional == True
        
        # 检查各层配置
        assert isinstance(config.haystack, HaystackConfig)
        assert isinstance(config.txtai, TxtaiConfig)
        assert isinstance(config.r2r, R2RConfig)
        assert isinstance(config.self_rag_validation, SelfRAGValidationConfig)
        assert isinstance(config.cache, CacheConfig)
        assert isinstance(config.monitoring, MonitoringConfig)
        assert isinstance(config.security, SecurityConfig)
    
    def test_layer_enabled_check(self):
        """测试层启用检查"""
        config = HierarchicalRAGConfig(enabled=True)
        
        # 默认情况下所有层都启用
        assert config.is_layer_enabled("haystack") == True
        assert config.is_layer_enabled("txtai") == True
        assert config.is_layer_enabled("r2r") == True
        assert config.is_layer_enabled("self_rag_validation") == True
        
        # 禁用整个层级RAG
        config.enabled = False
        assert config.is_layer_enabled("haystack") == False
        
        # 禁用特定层
        config.enabled = True
        config.haystack.enabled = False
        assert config.is_layer_enabled("haystack") == False
        assert config.is_layer_enabled("txtai") == True
    
    def test_get_layer_config(self):
        """测试获取层配置"""
        config = HierarchicalRAGConfig()
        
        haystack_config = config.get_layer_config("haystack")
        assert isinstance(haystack_config, HaystackConfig)
        assert haystack_config.max_workers == 10
        
        txtai_config = config.get_layer_config("txtai")
        assert isinstance(txtai_config, TxtaiConfig)
        assert txtai_config.similarity_threshold == 0.8
        
        invalid_config = config.get_layer_config("invalid_layer")
        assert invalid_config is None
    
    def test_config_validation(self):
        """测试配置验证"""
        # 有效配置
        config = HierarchicalRAGConfig(enabled=True)
        errors = config.validate_configuration()
        assert len(errors) == 0
        
        # 无效配置 - 所有层都禁用
        config.haystack.enabled = False
        config.txtai.enabled = False
        config.r2r.enabled = False
        config.self_rag_validation.enabled = False
        errors = config.validate_configuration()
        assert len(errors) > 0
        assert "至少需要启用一个处理层" in errors
        
        # 无效配置 - 参数超出范围
        config.haystack.enabled = True
        config.haystack.max_workers = -1
        errors = config.validate_configuration()
        assert any("max_workers 必须大于0" in error for error in errors)
        
        # 无效配置 - 阈值超出范围
        config.haystack.max_workers = 10
        config.txtai.enabled = True  # 需要启用txtai才能验证其配置
        config.txtai.similarity_threshold = 1.5
        errors = config.validate_configuration()
        assert any("similarity_threshold 必须在0-1之间" in error for error in errors)


class TestHaystackConfig:
    """测试Haystack配置"""
    
    def test_haystack_config_defaults(self):
        """测试Haystack配置默认值"""
        config = HaystackConfig()
        
        assert config.enabled == True
        assert config.max_workers == 10
        assert config.task_timeout == 300
        assert config.retry_attempts == 3
        assert config.retry_delay == 1.0
        assert config.enable_parallel_execution == True
        assert config.load_balancing == True
        assert config.performance_monitoring == True
    
    def test_haystack_config_custom(self):
        """测试Haystack配置自定义值"""
        config = HaystackConfig(
            max_workers=20,
            task_timeout=600,
            retry_attempts=5
        )
        
        assert config.max_workers == 20
        assert config.task_timeout == 600
        assert config.retry_attempts == 5


class TestTxtaiConfig:
    """测试txtai配置"""
    
    def test_txtai_config_defaults(self):
        """测试txtai配置默认值"""
        config = TxtaiConfig()
        
        assert config.enabled == True
        assert config.cve_database_url == "https://cve.circl.lu/api"
        assert config.cve_cache_ttl == 3600
        assert config.best_practices_sources == ["owasp", "sans", "nist"]
        assert config.historical_cases_limit == 100
        assert config.similarity_threshold == 0.8
        assert config.retrieval_timeout == 30
        assert config.enable_incremental_update == True
        assert config.knowledge_cache_size == 1000


class TestConfigIntegration:
    """测试配置集成"""
    
    def test_config_class_hierarchical_rag(self):
        """测试Config类的层级RAG配置"""
        # 检查默认配置
        assert hasattr(Config, 'hierarchical_rag')
        assert isinstance(Config.hierarchical_rag, HierarchicalRAGConfig)
        
        # 测试便捷方法
        assert Config.is_hierarchical_rag_enabled() == False  # 默认关闭
        assert Config.get_architecture_mode() == "hierarchical"
        
        # 测试层配置获取
        haystack_config = Config.get_hierarchical_layer_config("haystack")
        assert isinstance(haystack_config, HaystackConfig)
        
        # 测试层启用检查
        assert Config.is_hierarchical_layer_enabled("haystack") == False  # 因为整体未启用
    
    def test_config_load_from_dict(self):
        """测试从字典加载配置"""
        config_data = {
            "hierarchical_rag": {
                "enabled": True,
                "architecture_mode": "hierarchical",
                "haystack": {
                    "enabled": True,
                    "max_workers": 15
                },
                "txtai": {
                    "enabled": True,
                    "similarity_threshold": 0.9
                }
            }
        }
        
        Config.load_from_dict(config_data)
        
        assert Config.hierarchical_rag.enabled == True
        assert Config.hierarchical_rag.architecture_mode == "hierarchical"
        assert Config.hierarchical_rag.haystack.max_workers == 15
        assert Config.hierarchical_rag.txtai.similarity_threshold == 0.9
    
    def test_config_to_dict(self):
        """测试配置转换为字典"""
        config_dict = Config.to_dict()
        
        assert "hierarchical_rag" in config_dict
        assert "enabled" in config_dict["hierarchical_rag"]
        assert "haystack" in config_dict["hierarchical_rag"]
        assert "txtai" in config_dict["hierarchical_rag"]
        assert "r2r" in config_dict["hierarchical_rag"]
        assert "self_rag_validation" in config_dict["hierarchical_rag"]


class TestConfigMigrator:
    """测试配置迁移器"""
    
    def test_config_migrator_creation(self):
        """测试配置迁移器创建"""
        migrator = ConfigMigrator()
        
        assert migrator.migration_history == []
        assert migrator.backup_dir.exists()
    
    def test_migration_with_temp_config(self):
        """测试使用临时配置文件的迁移"""
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_config = {
                "global": {"language": "zh-CN"},
                "self_rag": {
                    "enabled": True,
                    "vector_store": "faiss",
                    "retrieval_k": 5
                },
                "project": {
                    "max_batch_size": 15
                }
            }
            yaml.dump(temp_config, f)
            temp_path = f.name
        
        try:
            migrator = ConfigMigrator()
            success = migrator.migrate_to_hierarchical_rag(temp_path)
            
            assert success == True
            
            # 检查迁移后的配置
            with open(temp_path, 'r', encoding='utf-8') as file:
                migrated_config = yaml.safe_load(file)
            
            assert "hierarchical_rag" in migrated_config
            assert migrated_config["hierarchical_rag"]["enabled"] == False  # 默认关闭
            assert migrated_config["hierarchical_rag"]["haystack"]["max_workers"] == 15  # 从project迁移
            
        finally:
            # 清理临时文件
            os.unlink(temp_path)
    
    def test_compatibility_check(self):
        """测试兼容性检查"""
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_config = {
                "global": {"language": "zh-CN"},
                "self_rag": {"enabled": True}
            }
            yaml.dump(temp_config, f)
            temp_path = f.name
        
        try:
            migrator = ConfigMigrator()
            result = migrator.check_compatibility(temp_path)
            
            assert result["compatible"] == False
            assert "缺少层级RAG配置" in result["issues"]
            assert "运行配置迁移工具" in result["recommendations"]
            
        finally:
            # 清理临时文件
            os.unlink(temp_path)


class TestConfigHotReloader:
    """测试配置热重载器"""
    
    def test_hot_reloader_creation(self):
        """测试热重载器创建"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump({"test": "config"}, f)
            temp_path = f.name
        
        try:
            reloader = ConfigHotReloader(temp_path)
            
            assert reloader.config_path == temp_path
            assert reloader.last_modified == 0
            assert reloader.callbacks == []
            
        finally:
            os.unlink(temp_path)
    
    def test_callback_management(self):
        """测试回调管理"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump({"test": "config"}, f)
            temp_path = f.name
        
        try:
            reloader = ConfigHotReloader(temp_path)
            
            callback_called = []
            
            def test_callback():
                callback_called.append(True)
            
            reloader.add_callback(test_callback)
            assert len(reloader.callbacks) == 1
            
        finally:
            os.unlink(temp_path)


if __name__ == "__main__":
    pytest.main([__file__])